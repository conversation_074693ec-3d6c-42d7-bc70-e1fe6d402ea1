before_script:
  - echo "this is .gitlab-ci.yml $CI_PIPELINE_SOURCE $CI_COMMIT_TAG, $CI_COMMIT_BRANCH"
  - echo "VARIABLE CI_COMMIT_SHA IS $CI_COMMIT_SHA!"
  - echo "VA<PERSON><PERSON>LE CI_COMMIT_TAG IS $CI_COMMIT_TAG!"
  - echo "VARIABLE CI_PROJECT_DIR IS $CI_PROJECT_DIR!"
  - echo "VARIABLE CI_COMMIT_REF_SLUG IS $CI_COMMIT_REF_SLUG!"

stages:
  - prepare
  - parallel
  - deploy_k8s

workflow:
  rules:
    - if: '$CI_COMMIT_TAG != null || $CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "api" || $CI_JOB_MANUAL == "true"'

variables:
  IMAGE_NAME: new-media-crawler
  APP_NAME: new-media-crawler
  NAMESPACE: new-media
  REPLICAS_PROD: 1
  REPLICAS_DEV: 1

docker_build_job:
  tags:
    - C3-Runner-Shell
  stage: parallel
  script:
    - docker build -t $CI_REGISTRY/fe/$APP_NAME:$CI_COMMIT_SHA .
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker push $CI_REGISTRY/fe/$APP_NAME:$CI_COMMIT_SHA
  rules:
    - if: '$CI_COMMIT_TAG != null'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

deploy:
  image: bitnami/kubectl:latest
  stage: deploy_k8s
  script:
    - echo "$CI_COMMIT_TAG"
    - |
      if [[ "$CI_COMMIT_REF_NAME" == "main" ]]; then
        sed -i "s/__APP_NAME__/${APP_NAME}/" k8s.yml
        sed -i "s/__REPLICAS__/${REPLICAS_PROD}/" k8s.yml
      fi
    - sed -i "s/__NAMESPACE__/${NAMESPACE}/" k8s.yml
    - sed -i "s/__VERSION__/${CI_COMMIT_SHA}/" k8s.yml
    - sed -i "s/__CI_REGISTRY__/${CI_REGISTRY}/" k8s.yml
    - sed -i "s/__IMAGE_NAME__/${IMAGE_NAME}/" k8s.yml
    - kubectl apply -f k8s.yml
  rules:
    - if: '$CI_COMMIT_TAG != null'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
