# 新媒体数据爬虫项目

基于 Crawlee + PlaywrightCrawler + Fastify + node-cron 的新媒体数据爬虫项目，支持 API 触发和定时任务，专门用于抓取和分析新媒体平台的数据。

## 🚀 主要功能

- **多平台数据抓取**: 支持多种新媒体平台的数据采集
- **智能数据分析**: 包含经营视图、私信分析、直播数据、视频数据等多维度分析
- **API 服务**: 提供 RESTful API 接口，支持手动触发和状态查询
- **定时任务**: 基于 node-cron 的自动化定时爬取
- **数据集成**: 支持飞书多维表格数据同步和 Webhook 通知
- **容器化部署**: 提供 Docker 和 Kubernetes 部署配置
- **状态监控**: 实时监控爬虫运行状态和执行结果

## 🛠 技术栈

- **爬虫框架**: [Crawlee 3.14.0](https://crawlee.dev/) - 现代化的网页爬虫和自动化框架
- **浏览器自动化**: [Playwright](https://playwright.dev/) - 跨浏览器自动化库
- **Web 服务**: [Fastify](https://www.fastify.io/) - 高性能的 Node.js Web 框架
- **定时任务**: [node-cron](https://github.com/node-cron/node-cron) - 基于 cron 的任务调度
- **第三方集成**: 飞书 API SDK - 数据同步和通知

## 📦 安装和运行

### 环境要求

- Node.js >= 20.0.0
- pnpm >= 8.0.0 (推荐) 或 npm

### 安装依赖

```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

### 环境配置

复制 `.env.example` 创建 `.env` 文件并配置必要的环境变量：

### 启动服务

```bash
# 开发模式 运行接口服务
pnpm start:dev

# 调试模式 直接运行爬虫本地调试 记得开 headless:false
pnpm dev

```

服务器将在 `http://localhost:3000` 启动。

## 📖 API 文档

### 健康检查

```bash
GET /health
```

返回服务器状态信息。

### 爬虫状态查询

```bash
GET /api/crawler/status
```

返回当前爬虫的运行状态、最后执行时间和结果。

### 手动触发爬虫 （常用，在 Cookie 过期的时候，粘贴新 Cookie 到多维表格，进而触发接口主动触发爬虫任务）

```bash
POST /api/crawler/run

curl -X POST https://new-media-crawler.xiaofeilun.cn/api/crawler/run
```


## 📁 项目结构

```text
new-media-crawler/
├── src/                          # 源代码目录
│   ├── main.ts                   # 应用入口文件
│   ├── server.ts                 # Fastify 服务器配置
│   ├── crawler.ts                # 爬虫核心逻辑和状态管理
│   ├── routes.ts                 # 爬虫路由处理器
│   ├── debug.ts                  # 调试入口文件
│   ├── api/                      # API 相关模块
│   │   ├── data-report.ts        # 数据上报接口
│   │   └── webhook.ts            # Webhook 通知
│   ├── module/                   # 功能模块
│   │   └── analysis/             # 数据分析模块
│   │       ├── leads.ts          # 线索数据分析
│   │       ├── message.ts        # 私信数据分析
│   │       ├── live.ts           # 直播数据分析
│   │       └── video.ts          # 视频数据分析
│   └── utils/                    # 工具函数
│       ├── bitable.ts            # 飞书多维表格工具
│       ├── cookie.ts             # Cookie 处理工具
│       ├── data.ts               # 数据处理工具
│       ├── dom.ts                # DOM 操作工具
│       └── setDataBitable.ts     # 数据写入工具
├── dist/                         # 编译输出目录
├── storage/                      # Crawlee 存储目录
├── docs/                         # 文档目录
├── Dockerfile                    # Docker 构建文件
├── k8s.yml                       # Kubernetes 部署配置
├── tsconfig.json                 # TypeScript 配置
├── package.json                  # 项目依赖配置
└── pnpm-lock.yaml               # 依赖锁定文件
```

## ⚙️ 配置说明

### 定时任务配置

项目默认配置了定时任务，可在 `src/server.ts` 中修改：

```typescript
// 当前配置：每 15 分钟执行一次 (测试用)
const cronExpression = '*/15 * * * *';

// 生产环境建议配置：每日 10:00, 11:00, 12:00 执行
// const cronExpression = '0 0 10,11,12 * * *';
```

### 爬虫路由配置

项目支持多种数据分析类型，在 `src/routes.ts` 中定义：

- `OVERVIEW`: 经营视图数据分析
- `MESSAGE`: 私信数据分析
- `LIVE-LIST`: 直播数据分析
- `VIDEO-LIST`: 视频数据分析

### Cookie 和认证

车云店与懂车云店账号从飞书多维表格获取 Cookie 配置，参见 `utils/bitable.ts` 文件。

## 🔧 开发指南

1. 在 `src/module/analysis/` 目录下创建要爬取对应页面的 dom 选择器方法
2. 当前 routes 的爬取逻辑基本类似，所以封装进统一的一个方法 `processSubAccounts` 该方法包含点击切换到子账户，然后遍历的勾选子账户收集相关数据，车云店平台是失去焦点后才请求接口
