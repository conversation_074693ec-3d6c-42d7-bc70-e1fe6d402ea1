{"name": "new-media-crawler", "version": "0.0.1", "type": "module", "description": "This is an example of a Crawlee project.", "dependencies": {"@larksuiteoapi/node-sdk": "^1.53.0", "crawlee": "3.14.0", "dotenv": "^17.2.1", "fastify": "^5.4.0", "node-cron": "^4.2.1", "pino-pretty": "^13.0.0", "playwright": "*"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/node": "^22.0.0", "@types/node-cron": "^3.0.11", "tsx": "^4.4.0", "typescript": "~5.8.0"}, "scripts": {"start": "npm run start:dev", "start:prod": "node dist/main.js", "start:dev": "tsx watch src/main.ts", "dev": "tsx watch src/debug.ts", "build": "tsc", "crawler:run": "tsx src/crawler.ts", "postinstall": "npx crawlee install-playwright-browsers"}, "packageManager": "pnpm@10.13.1", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC"}