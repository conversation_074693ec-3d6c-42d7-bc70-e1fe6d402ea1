// API 接口统一管理
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { log } from 'crawlee';
import { crawlerManager, runCrawler } from './crawler.js';
import { dongcheyundianDataSync } from './fetch-schedule/dongcheyundian/index.js';
import { cheyundianToyoTaBIDataSyncHandler } from './fetch-schedule/cheyundian-toyota-bi/index.js';
import { cheyundianToyotaPerformanceDataSyncHandler } from './fetch-schedule/cheyundian-toyota-performance/index.js';
import { cheyundianToyotaShortVideoDataSyncHandler } from './fetch-schedule/cheyundian-toyota-short-video/index.js';

// 接口响应类型定义
export type ApiResponse<T = any> = {
  success: boolean;
  message?: string;
  data?: T;
  timestamp?: string;
};

export type HealthCheckResponse = {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
};

export type CrawlerStatusData = {
  status: 'idle' | 'running' | 'error' | 'completed';
  lastRunTime: Date | null;
  lastError: string | null;
  lastResult: any;
};

export type CrawlerStatusResponse = ApiResponse<CrawlerStatusData> & {
  success: true;
  data: CrawlerStatusData;
};

// HTTP 状态码枚举
export enum HttpStatusCode {
  OK = 200,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
}

// 错误类型定义
export type ApiError = {
  code: string;
  message: string;
  details?: any;
};

/**
 * 健康检查接口
 * @param _request Fastify 请求对象（未使用）
 * @param _reply Fastify 响应对象（未使用）
 * @returns 健康检查响应
 */
export const healthCheckHandler = async (
  _request: FastifyRequest,
  _reply: FastifyReply
): Promise<HealthCheckResponse> => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  };
};

/**
 * 获取爬虫状态接口
 * @param _request Fastify 请求对象（未使用）
 * @param _reply Fastify 响应对象（未使用）
 * @returns 爬虫状态响应
 */
export const getCrawlerStatusHandler = async (
  _request: FastifyRequest,
  _reply: FastifyReply
): Promise<CrawlerStatusResponse> => {
  const status = crawlerManager.getStatus();
  return {
    success: true,
    data: status,
  };
};

/**
 * 手动触发爬虫接口
 * @param _request Fastify 请求对象（未使用）
 * @param reply Fastify 响应对象
 * @returns API 响应
 */
export const runCrawlerHandler = async (
  _request: FastifyRequest,
  reply: FastifyReply
): Promise<ApiResponse> => {
  try {
    log.info('收到手动触发爬虫请求');

    // 检查是否已有爬虫在运行
    if (crawlerManager.isRunning()) {
      return reply.code(HttpStatusCode.CONFLICT).send({
        success: false,
        message: '爬虫正在运行中，请等待当前任务完成',
        timestamp: new Date().toISOString(),
      });
    }

    // 异步运行爬虫，不阻塞响应
    runCrawler().catch((error: Error) => {
      log.error('爬虫运行失败:', error);
    });

    return {
      success: true,
      message: '爬虫任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('启动爬虫失败:', { error });
    return reply.code(HttpStatusCode.INTERNAL_SERVER_ERROR).send({
      success: false,
      message: error instanceof Error ? error.message : '启动爬虫失败',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * 手动触发懂车云店数据同步接口
 * @param _request Fastify 请求对象（未使用）
 * @param reply Fastify 响应对象
 * @returns API 响应
 */
export const dongcheyundianDataSyncHandler = async (
  _request: FastifyRequest,
  reply: FastifyReply
): Promise<ApiResponse> => {
  try {
    log.info('收到手动触发懂车云店数据同步请求');

    dongcheyundianDataSync().catch((error: Error) => {
      log.error('懂车云店数据同步失败:', error);
    });

    return {
      success: true,
      message: '懂车云店数据同步任务已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    log.error('懂车云店数据同步失败', { error });
    return reply.code(HttpStatusCode.INTERNAL_SERVER_ERROR).send({
      success: false,
      message: error instanceof Error ? error.message : '懂车云店数据同步失败',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * 注册所有 API 接口
 * @param fastify Fastify 实例
 */
export const registerApiRoutes = (fastify: FastifyInstance) => {
  // 检查服务器健康状态
  fastify.get('/health', healthCheckHandler);

  // 获取爬虫运行状态
  fastify.get('/api/crawler/status', getCrawlerStatusHandler);

  // 手动触发爬虫任务
  fastify.post('/api/crawler/run', runCrawlerHandler);

  // 手动触发懂车云店数据同步
  fastify.post('/api/dongcheyundian/datasync', dongcheyundianDataSyncHandler);

  // 手动触发TOYOTA车云店BI数据同步
  fastify.post(
    '/api/cheyundian-toyota-bi/datasync',
    cheyundianToyoTaBIDataSyncHandler
  );

  // 手动触发TOYOTA车云店业绩指标相关数据同步
  fastify.post(
    '/api/cheyundian-toyota-performance/datasync',
    cheyundianToyotaPerformanceDataSyncHandler
  );

  // 手动触发TOYOTA车云店短视频数据同步
  fastify.post(
    '/api/cheyundian-toyota-short-video/datasync',
    cheyundianToyotaShortVideoDataSyncHandler
  );

  log.info('所有 API 接口已注册完成');
};
