import { log } from 'crawlee';
import { sendMessage } from './webhook.js';

const userDataReportUrl =
  'https://new-media-dev.xiaofeilun.cn/new-media-api/project/performance/data-report/user';

/**
 * 用户维度数据上报
 */
export type UserImportData = {
  /**
   * 广告线索数量
   */
  adClueCount?: number;
  /**
   * 新媒体线索数量
   */
  clueCountTotal?: number;
  /**
   * 直播投流消耗
   */
  liveAdCost?: number;
  /**
   * 直播人均观看时长（秒）
   */
  liveAvgWatchDuration?: number;
  /**
   * 直播间线索转化率
   */
  liveClueConvertRate?: number;
  /**
   * 直播间线索成本
   */
  liveCostPerClue?: number;
  /**
   * 直播间表单线索量
   */
  liveFormClueCount?: number;
  /**
   * 直播间互动率
   */
  liveInteractRate?: number;
  /**
   * 直播间引导私信 24 小时留资次数
   */
  livePrivateMsg24hClueCount?: number;
  /**
   * 直播间场次
   */
  liveSessionCount?: number;
  /**
   * 自然线索数量
   */
  natureClueCount?: number;
  /**
   * 作品投流消耗
   */
  postAdCost?: number;
  /**
   * 短视频-线索转化率
   */
  postClueConvertRate?: number;
  /**
   * 短视频-线索量
   */
  postClueCount?: number;
  /**
   * 短视频-单条线索成本
   */
  postCostPerClue?: number;
  /**
   * 新发布视频数
   */
  postCount?: number;
  /**
   * 短视频-引导私信线索量
   */
  postPrivateMsgClueCount?: number;
  /**
   * 短视频-引流直播间次数
   */
  postToLiveRedirectCount?: number;
  /**
   * 私信线索人数
   */
  privateMsgClueUcount?: number;
  /**
   * 私信开口人数
   */
  privateMsgOpenerUcount?: number;
  /**
   * 私信接收消息数
   */
  privateMsgReceiveCount?: number;
  /**
   * 进入私信会话人数
   */
  privateMsgSessionUcount?: number;
  /**
   * 3分钟回复率，范围 0~1
   */
  reply3minRate?: number;
  /**
   * 外显ID
   */
  showAccountId: string;
  /**
   * 统计日期
   */
  statDate: string;
  /**
   * 广告消耗（投流费用金额）
   */
  totalAdCost?: number;
};

export const uploadUserData = async (data: UserImportData[]) => {
  try {
    const urlWithQuery = new URL(userDataReportUrl);
    // 写死 Toyota 项目的 ID
    urlWithQuery.searchParams.append('projectId', '71');
    const response = await fetch(urlWithQuery.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    log.info('数据上报完成:', result);

    const isDev = urlWithQuery.toString().includes('dev');

    if (result.code == 0) {
      const content = `**同步成功${
        isDev ? '（测试环境）' : ' (正式环境)'
      }**\n\n- 同步表: Toyota用户维度数据表\n- 同步条数：${
        data.length
      }\n- 同步时间：${result.time}`;
      sendMessage('Toyota项目用户维度数据上报', content, 'success', true);
    } else {
      const content = `**同步失败${
        isDev ? '（测试环境）' : ' (正式环境)'
      }**\n\n- 同步表: Toyota用户维度数据表\n- 错误信息：${
        result.message
      }\n- 错误代码：${result.code}`;
      sendMessage('Toyota项目用户维度数据上报', content, 'fail', true);
    }
  } catch (error) {
    log.error('数据上报失败:', { error });
  }
};
