// 爬虫核心逻辑模块
import 'dotenv/config';
import { KeyValueStore, PlaywrightCrawler, RequestQueue, log } from 'crawlee';
import { router } from './routes.js';
import { getAllMergedAccountData } from './utils/data.js';
import { writeFileSync } from 'fs';
import { parseCookieString } from './utils/cookie.js';
import { uploadUserData } from './api/data-report.js';
import { getCheYunDianCookieByBitable } from './utils/bitable.js';
import { insertCollectedDataToBitable } from './utils/setDataBitable.js';
import { sendMessage } from './api/webhook.js';

// 爬虫运行状态
export enum CrawlerStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  ERROR = 'error',
  COMPLETED = 'completed',
}

// 爬虫状态管理
class CrawlerManager {
  private status: CrawlerStatus = CrawlerStatus.IDLE;
  private lastRunTime: Date | null = null;
  private lastError: string | null = null;
  private lastResult: any = null;

  getStatus() {
    return {
      status: this.status,
      lastRunTime: this.lastRunTime,
      lastError: this.lastError,
      lastResult: this.lastResult,
    };
  }

  setStatus(status: CrawlerStatus) {
    this.status = status;
  }

  setError(error: string) {
    this.lastError = error;
    this.status = CrawlerStatus.ERROR;
  }

  setCompleted(result: any) {
    this.status = CrawlerStatus.COMPLETED;
    this.lastRunTime = new Date();
    this.lastResult = result;
    this.lastError = null;
  }

  isRunning() {
    return this.status === CrawlerStatus.RUNNING;
  }
}

// 全局爬虫管理器实例
export const crawlerManager = new CrawlerManager();

// 爬虫配置
const startUrls = [
  {
    url: 'https://leads.cluerich.com/pc/analysis/leads/overview',
    userData: {
      label: 'OVERVIEW', // 数据分析-经营视图
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/message',
    userData: {
      label: 'MESSAGE', // 数据分析-私信分析
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/live-list',
    userData: {
      label: 'LIVE-LIST', // 数据分析-直播列表
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/short-video/list',
    userData: {
      label: 'VIDEO-LIST', // 数据分析-短视频列表
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/leads/overview?fake_login_token=PKbSGeLivwt-K66AwVKFSoPmBQMb4Oa2yWoYeSSU-I083Pd1RmxlU-KXrodQUL0A',
    userData: {
      label: 'SPECIAL-OVERVIEW', // 特殊账号-数据分析-经营视图
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/message?fake_login_token=PKbSGeLivwt-K66AwVKFSoPmBQMb4Oa2yWoYeSSU-I083Pd1RmxlU-KXrodQUL0A',
    userData: {
      label: 'SPECIAL-MESSAGE', // 特殊账号-数据分析-私信分析
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/live-list?fake_login_token=PKbSGeLivwt-K66AwVKFSoPmBQMb4Oa2yWoYeSSU-I083Pd1RmxlU-KXrodQUL0A',
    userData: {
      label: 'SPECIAL-LIVE-LIST', // 特殊账号-数据分析-直播列表
    },
  },
  {
    url: 'https://leads.cluerich.com/pc/analysis/short-video/list?fake_login_token=PKbSGeLivwt-K66AwVKFSoPmBQMb4Oa2yWoYeSSU-I083Pd1RmxlU-KXrodQUL0A',
    userData: {
      label: 'SPECIAL-VIDEO-LIST', // 特殊账号-数据分析-短视频列表
    },
  },
];

/**
 * 运行爬虫的核心函数
 */
export async function runCrawler(): Promise<any> {
  // 检查是否已有爬虫在运行
  if (crawlerManager.isRunning()) {
    throw new Error('爬虫正在运行中，请等待当前任务完成');
  }

  try {
    crawlerManager.setStatus(CrawlerStatus.RUNNING);
    log.info('开始运行新媒体数据爬虫...');

    try {
      // 清空自定义 KeyValueStore
      const kvStore = await KeyValueStore.open('account-merge-temp');
      await kvStore.drop();
      log.info('自定义存储数据清空完成');
    } catch (error) {
      log.info('清空自定义存储数据时出错（可能是首次运行）:', { error });
    }

    try {
      // 清空默认 RequestQueue - 这是关键！
      const requestQueue = await RequestQueue.open();
      await requestQueue.drop();
      log.info('请求队列清空完成');
    } catch (error) {
      log.info('清空请求队列时出错（可能是首次运行）:', { error });
    }

    // 创建爬虫实例
    const crawler = new PlaywrightCrawler({
      requestHandler: router,
      maxRequestsPerCrawl: 10,
      // 调试时候请设置为 false 以显示浏览器窗口
      // headless: process.env.NODE_ENV === 'production' ? true : false,
      headless: true,

      maxRequestRetries: 3,

      // 增加请求处理超时时间
      requestHandlerTimeoutSecs: 600,

      // 增加导航超时时间
      navigationTimeoutSecs: 60,

      // 禁用会话池持久化，避免存储文件问题
      persistCookiesPerSession: false,

      // 浏览器启动配置
      launchContext: {
        launchOptions: {
          args: [
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
          ],
        },
      },

      // 页面导航前的钩子 - 设置 Cookie
      preNavigationHooks: [
        async ({ request, page, log }) => {
          log.info(`开始处理请求: ${request.url}`);
          log.info('设置登录 Cookie...');
          try {
            // 解析并添加 cookies
            const cheYunDianCookie = await getCheYunDianCookieByBitable();

            if (!cheYunDianCookie) {
              throw new Error('未能获取到有效的Cookie');
            }

            const cookies = parseCookieString(
              cheYunDianCookie,
              '.cluerich.com'
            );
            // 添加 cookies 到页面上下文
            await page.context().addCookies(cookies);
            log.info('Cookie 设置完成');
          } catch (error) {
            log.error('Cookie 设置失败:', error as Error);
            throw error;
          }
        },
      ],

      // 错误处理
      errorHandler: async ({ request, log, error, crawler }) => {
        const errorMessage = error instanceof Error ? error.message : String(error);
        log.error(`请求 ${request.url} 失败:`, error as Error);

        // 检查是否为 TimeoutError，如果是则停止爬虫
        if (errorMessage.includes('TimeoutError') ||
            (error as any)?.name === 'TimeoutError') {
          log.error('检测到 TimeoutError，停止爬虫运行');
          const content = `检测到超时错误，爬虫已停止运行\n错误信息：${errorMessage}\n`;
          sendMessage('爬虫因超时错误停止', content, 'fail');

          // 停止爬虫，不重试也不执行后续任务
          await crawler.teardown();
          throw new Error('爬虫因 TimeoutError 停止运行');
        }

        const content = `错误信息：${errorMessage}\n`;
        sendMessage('爬虫运行失败', content, 'fail');
      },
    });

    // 运行爬虫
    await crawler.run(startUrls);
    log.info('爬虫运行完成！');

    // 获取合并后的账号数据
    const allAccountData = await getAllMergedAccountData(log);

    // 上传数据到原有API
    await uploadUserData(allAccountData);

    // 插入数据到飞书表格
    await insertCollectedDataToBitable(allAccountData);

    // 测试时本地看数据使用，将数据保存为 JSON 文件
    if (process.env.COLLECT_DATA === 'true') {
      writeFileSync(
        'collected-data.json',
        JSON.stringify(allAccountData, null, 2)
      );
      log.info('数据已保存到 collected-data.json 文件');
    }

    // 设置完成状态
    const result = {
      totalAccounts: allAccountData.length,
      data: allAccountData,
      timestamp: new Date().toISOString(),
    };

    crawlerManager.setCompleted(result);

    // 爬取完成后清空存储和队列，为下次运行做准备
    try {
      const kvStore = await KeyValueStore.open('account-merge-temp');
      await kvStore.drop();
      log.info('自定义存储数据清空完成');
    } catch (error) {
      log.info('清空自定义存储数据时出错:', { error });
    }

    try {
      // 清空请求队列，确保下次运行时有干净的状态
      const requestQueue = await RequestQueue.open();
      await requestQueue.drop();
      log.info('请求队列清空完成');
    } catch (error) {
      log.info('清空请求队列时出错:', { error });
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    log.error('爬虫运行失败:', error as Error);
    crawlerManager.setError(errorMessage);
    throw error;
  }
}
