// 调试脚本 - 直接运行 runCrawler 函数
import 'dotenv/config';
import { log } from 'crawlee';
import { runCrawler } from './crawler.js';

async function debugCrawler() {
  try {
    log.info('🚀 开始调试运行爬虫...');
    log.info('环境变量检查:', {
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT
    });

    const result = await runCrawler();

    log.info('✅ 爬虫调试运行完成!', {
      totalAccounts: result.totalAccounts,
      timestamp: result.timestamp
    });

    // 可以在这里添加更多调试信息
    console.log('📊 收集到的数据:', JSON.stringify(result.data, null, 2));

  } catch (error) {
    log.error('❌ 爬虫调试运行失败:', {error});
    process.exit(1);
  }
}

// 运行调试
debugCrawler();
