export const liveTableBody = {
  version: 4,
  metaData: {
    appId: 1002649,
  },
  reportId: 48967,
  dataSourceId: 10070,
  query: {
    dataSetId: 2211620,
    dataSetIdList: [2211620],
    fabricBlendingModelInfo: {},
    transform: {
      type: "table",
    },
    groupByIdList: [
      "1700038977648",
      "1700038981733",
      "1700038981733",
      "1700038977653",
      "1700038977680",
      "1700038977666",
      "1700038977657",
      "1700038977659",
      "1700038977661",
      "1700038977665",
      "1700038977676",
      "1700038977646",
      "1700038977649",
      "1700038977695",
      "1700038977696",
      "1700038977644",
      "1700038977647",
    ],
    selectIdList: [],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        "1700038977648",
        "1700038981733",
        "1700038981733",
        "1700038977653",
        "1700038977680",
        "1700038977666",
        "1700038977657",
        "1700038977659",
        "1700038977661",
        "1700038977665",
        "1700038977676",
        "1700038977646",
        "1700038977649",
        "1700038977695",
        "1700038977696",
        "1700038977644",
        "1700038977647",
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [],
    whereList: [
      {
        nodeType: 1,
        op: "and",
        val: [
          {
            name: "开播时间",
            id: "1700038977646",
            preRelation: "and",
            uniqueId: 250529152017516,
            op: "last",
            option: {
              dateMode: "relative",
              isReportFilter: false,
              isWhereInAggr: true,
            },
            val: [30],
            valOption: {
              datetimeUnit: "day",
              anchorOffset: 1,
              hourSharp: true,
            },
          },
        ],
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    pagination: {
      frontOnlyOffset: 1250,
      size: 50000,
      queryKey: "1f2743a4-723b-4ab6-97cf-a2286a8c61b8",
      offset: 0,
    },
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700038977648,
        format: {},
        id: "1700038977648",
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977648",
        roleType: 0,
        type: "string",
        uniqueId: 250529140241044,
      },
      {
        aggrConf: {},
        dimMetId: 1700038981733,
        format: {},
        id: "1700038981733",
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038981733",
        roleType: 0,
        type: "string",
        uniqueId: 250529140241087,
      },
      {
        aggrConf: {},
        dimMetId: 1700038981733,
        format: {},
        id: "1700038981733",
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038981733",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904104,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977653,
        format: {},
        id: "1700038977653",
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977653",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904145,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977680,
        format: {},
        id: "1700038977680",
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977680",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904194,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977666,
        format: {},
        id: "1700038977666",
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977666",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904235,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977657,
        format: {},
        id: "1700038977657",
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977657",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904276,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977659,
        format: {},
        id: "1700038977659",
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977659",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904319,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977661,
        format: {},
        id: "1700038977661",
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977661",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904362,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977665,
        format: {},
        id: "1700038977665",
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977665",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904403,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977676,
        format: {},
        id: "1700038977676",
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977676",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904464,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977646,
        format: {},
        id: "1700038977646",
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977646",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904511,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977649,
        format: {},
        id: "1700038977649",
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977649",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904520,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977695,
        format: {
          contentType: "link",
        },
        id: "1700038977695",
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977695",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904601,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977696,
        format: {
          contentType: "link",
        },
        id: "1700038977696",
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977696",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904642,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977644,
        format: {},
        id: "1700038977644",
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977644",
        roleType: 0,
        type: "string",
        uniqueId: 250529152017260,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977647,
        format: {},
        id: "1700038977647",
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977647",
        roleType: 0,
        type: "string",
        uniqueId: 250529161335061,
      },
    ],
    display: {
      conf: {
        alignDimension: "left",
        alignMeasure: "right",
        alternateRow: true,
        alternateRowColor: "#FBFBFC",
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: "#141414",
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: "tight",
        columnWidth: [],
        compact: false,
        compactDirection: "horizontal",
        customFields: {
          enable: true,
        },
        display: "standard",
        fixedIndex: -1,
        gridLineColor: "#E1E4E8",
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: "solid",
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: "solid",
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: "solid",
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: "#EEF1F5",
        headerFontBold: true,
        headerFontColor: "#1B1F23",
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: "row",
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 10,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: "loose",
        sortable: false,
        specialValue: {
          dimensions: "null",
          measures: "bracketTxt",
        },
        tableStyle: "standard",
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: "table",
      type: "table",
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [],
    pagination: {
      frontOnlyOffset: 1250,
      size: 50000,
      queryKey: "1f2743a4-723b-4ab6-97cf-a2286a8c61b8",
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: "Normal",
      structType: "LeftRight",
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dimMetId: 250529152017451,
        filter: {
          op: "and",
          children: [
            {
              aggrConf: {},
              dataSetId: 2211620,
              dimMetId: 1700038977646,
              disableMenuKey: ["addOrFilter", "subFilter"],
              filter: {
                op: "last",
                option: {
                  dateMode: "relative",
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  datetimeUnit: "day",
                  anchorOffset: 1,
                  hourSharp: true,
                },
              },
              format: {},
              id: "1700038977646",
              inCombinationPill: true,
              isMetric: false,
              location: "whereList",
              name: "开播时间",
              originId: "1700038977646",
              preRelation: "and",
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250529152017516,
              unremovable: true,
            },
          ],
        },
        format: {
          displayName: "组合筛选",
        },
        highlight: false,
        id: "250529152017451",
        index: 0,
        location: "whereList",
        nameIndex: 1,
        notJoinQuery: false,
        originId: "250529152017451",
        pillType: "combination_filter",
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250529152017451,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
  display: {
    conf: {
      alignDimension: "left",
      alignMeasure: "right",
      alternateRow: true,
      alternateRowColor: "#FBFBFC",
      autoWrap: false,
      bodyFontBold: false,
      bodyFontColor: "#141414",
      bodyFontItalic: false,
      bodyFontSize: 12,
      bodyFontUnderline: false,
      colPadding: null,
      colSpaceMode: "tight",
      columnWidth: [],
      compact: false,
      compactDirection: "horizontal",
      customFields: {
        enable: true,
      },
      display: "standard",
      fixedIndex: -1,
      gridLineColor: "#E1E4E8",
      gridLineFrame: true,
      gridLineFrameColor: null,
      gridLineFrameStyle: "solid",
      gridLineFrameWidth: 1,
      gridLineHorizontal: true,
      gridLineHorizontalColor: null,
      gridLineHorizontalStyle: "solid",
      gridLineHorizontalWidth: 1,
      gridLineVertical: true,
      gridLineVerticalColor: null,
      gridLineVerticalStyle: "solid",
      gridLineVerticalWidth: 1,
      headerBackground: true,
      headerColor: "#EEF1F5",
      headerFontBold: true,
      headerFontColor: "#1B1F23",
      headerFontItalic: false,
      headerFontSize: 12,
      headerFontUnderline: false,
      headerMenu: true,
      headerSubTitleFontBold: false,
      headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
      headerSubTitleFontItalic: false,
      headerSubTitleFontSize: 12,
      headerSubTitleFontUnderline: false,
      hideHeader: false,
      hoverHighlight: "row",
      lineNumber: false,
      loadPartialData: false,
      measureFirst: false,
      pageSize: 10,
      pagination: true,
      rowPadding: null,
      rowSpaceMode: "loose",
      sortable: false,
      specialValue: {
        dimensions: "null",
        measures: "bracketTxt",
      },
      tableStyle: "standard",
      transpose: false,
      version: 33,
    },
    enableAdvisor: true,
    queryType: "table",
    type: "table",
    fieldsFormat: {
      "1700038977648": {},
      "1700038981733": {},
      "1700038977653": {},
      "1700038977680": {},
      "1700038977666": {},
      "1700038977657": {},
      "1700038977659": {},
      "1700038977661": {},
      "1700038977665": {},
      "1700038977676": {},
      "1700038977646": {},
      "1700038977649": {},
      "1700038977695": {
        contentType: "link",
      },
      "1700038977696": {
        contentType: "link",
      },
      "1700038977644": {},
      "1700038977647": {},
    },
  },
  originalSchema: {
    cache: {
      cacheVersion: "V1",
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700038977648,
        format: {},
        id: "1700038977648",
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977648",
        roleType: 0,
        type: "string",
        uniqueId: 250529140241044,
      },
      {
        aggrConf: {},
        dimMetId: 1700038981733,
        format: {},
        id: "1700038981733",
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038981733",
        roleType: 0,
        type: "string",
        uniqueId: 250529140241087,
      },
      {
        aggrConf: {},
        dimMetId: 1700038981733,
        format: {},
        id: "1700038981733",
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038981733",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904104,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977653,
        format: {},
        id: "1700038977653",
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977653",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904145,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977680,
        format: {},
        id: "1700038977680",
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977680",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904194,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977666,
        format: {},
        id: "1700038977666",
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977666",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904235,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977657,
        format: {},
        id: "1700038977657",
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977657",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904276,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977659,
        format: {},
        id: "1700038977659",
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977659",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904319,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977661,
        format: {},
        id: "1700038977661",
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977661",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904362,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977665,
        format: {},
        id: "1700038977665",
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977665",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904403,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977676,
        format: {},
        id: "1700038977676",
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977676",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904464,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977646,
        format: {},
        id: "1700038977646",
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977646",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904511,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977649,
        format: {},
        id: "1700038977649",
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977649",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904520,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977695,
        format: {
          contentType: "link",
        },
        id: "1700038977695",
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977695",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904601,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977696,
        format: {
          contentType: "link",
        },
        id: "1700038977696",
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977696",
        roleType: 0,
        type: "string",
        uniqueId: 250529142904642,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977644,
        format: {},
        id: "1700038977644",
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977644",
        roleType: 0,
        type: "string",
        uniqueId: 250529152017260,
      },
      {
        aggrConf: {},
        dimMetId: 1700038977647,
        format: {},
        id: "1700038977647",
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: "dimensions",
        originId: "1700038977647",
        roleType: 0,
        type: "string",
        uniqueId: 250529161335061,
      },
    ],
    display: {
      conf: {
        alignDimension: "left",
        alignMeasure: "right",
        alternateRow: true,
        alternateRowColor: "#FBFBFC",
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: "#141414",
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: "tight",
        columnWidth: [],
        compact: false,
        compactDirection: "horizontal",
        customFields: {
          enable: true,
        },
        display: "standard",
        fixedIndex: -1,
        gridLineColor: "#E1E4E8",
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: "solid",
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: "solid",
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: "solid",
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: "#EEF1F5",
        headerFontBold: true,
        headerFontColor: "#1B1F23",
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: "row",
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 10,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: "loose",
        sortable: false,
        specialValue: {
          dimensions: "null",
          measures: "bracketTxt",
        },
        tableStyle: "standard",
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: "table",
      type: "table",
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [],
    pagination: {
      frontOnlyOffset: 1250,
      size: 50000,
      queryKey: "1f2743a4-723b-4ab6-97cf-a2286a8c61b8",
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: "Normal",
      structType: "LeftRight",
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dimMetId: 250529152017451,
        filter: {
          op: "and",
          children: [
            {
              aggrConf: {},
              dataSetId: 2211620,
              dimMetId: 1700038977646,
              disableMenuKey: ["addOrFilter", "subFilter"],
              filter: {
                op: "last",
                option: {
                  dateMode: "relative",
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  datetimeUnit: "day",
                  anchorOffset: 1,
                  hourSharp: true,
                },
              },
              format: {},
              id: "1700038977646",
              inCombinationPill: true,
              isMetric: false,
              location: "whereList",
              name: "开播时间",
              originId: "1700038977646",
              preRelation: "and",
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250529152017516,
              unremovable: true,
            },
          ],
        },
        format: {
          displayName: "组合筛选",
        },
        highlight: false,
        id: "250529152017451",
        index: 0,
        location: "whereList",
        nameIndex: 1,
        notJoinQuery: false,
        originId: "250529152017451",
        pillType: "combination_filter",
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250529152017451,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
};
