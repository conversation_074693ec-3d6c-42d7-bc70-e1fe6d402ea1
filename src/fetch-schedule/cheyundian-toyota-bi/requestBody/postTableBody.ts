export const postTableBody = {
  version: 4,
  metaData: {
    appId: 1002649,
  },
  reportId: 48969,
  dataSourceId: 10070,
  query: {
    dataSetId: 2213514,
    dataSetIdList: [2213514],
    fabricBlendingModelInfo: {},
    transform: {
      type: "table",
    },
    groupByIdList: [
      "1700039018961",
      "1700039042169",
      "1700039018977",
      "1700039018978",
      "1700039018979",
      "1700039018980",
      "1700039018958",
      "1700039018974",
      "1700042191295",
      "1700039018960",
      "1700039018959",
    ],
    selectIdList: ["1700039148675"],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        "1700039018961",
        "1700039042169",
        "1700039018977",
        "1700039018978",
        "1700039018979",
        "1700039018980",
        "1700039018958",
        "1700039018974",
        "1700042191295",
        "1700039018960",
        "1700039018959",
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [],
    whereList: [
      {
        nodeType: 1,
        op: "and",
        val: [
          {
            name: "发布日期",
            id: "1700039018955",
            preRelation: "and",
            uniqueId: 250529152850220,
            op: "last",
            option: {
              isReportFilter: false,
              dateMode: "relative",
              isWhereInAggr: true,
            },
            val: [30],
            valOption: {
              datetimeUnit: "day",
              hourSharp: true,
              anchorOffset: 1,
            },
          },
        ],
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    pagination: {
      queryKey: "619886fc-ef8a-4821-b527-1e45706a0dd6",
      frontOnlyOffset: 10,
      size: 50000,
      offset: 0,
    },
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      enable: true,
      cacheVersion: "V1",
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    pagination: {
      queryKey: "619886fc-ef8a-4821-b527-1e45706a0dd6",
      frontOnlyOffset: 10,
      size: 50000,
    },
    cache: {
      enable: true,
      cacheVersion: "V1",
      expire: 300,
    },
    reportFilterConfig: {
      structType: "LeftRight",
      layoutSize: "Normal",
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {
          contentType: "link",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018961,
        location: "dimensions",
        uniqueId: 250529142904924,
        isGeoField: false,
        id: "1700039018961",
        type: "string",
        originId: "1700039018961",
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039042169,
        location: "dimensions",
        uniqueId: 250529143646067,
        isGeoField: false,
        id: "1700039042169",
        type: "string",
        originId: "1700039042169",
      },
      {
        roleType: 0,
        index: 2,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018977,
        location: "dimensions",
        uniqueId: 250529143646124,
        isGeoField: false,
        id: "1700039018977",
        type: "string",
        originId: "1700039018977",
      },
      {
        roleType: 0,
        index: 3,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018978,
        location: "dimensions",
        uniqueId: 250529143646165,
        isGeoField: false,
        id: "1700039018978",
        type: "string",
        originId: "1700039018978",
      },
      {
        roleType: 0,
        index: 4,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018979,
        location: "dimensions",
        uniqueId: 250529143646206,
        isGeoField: false,
        id: "1700039018979",
        type: "string",
        originId: "1700039018979",
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018980,
        location: "dimensions",
        uniqueId: 250529143646247,
        isGeoField: false,
        id: "1700039018980",
        type: "string",
        originId: "1700039018980",
      },
      {
        roleType: 0,
        index: 6,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018958,
        location: "dimensions",
        uniqueId: 250529143646294,
        isGeoField: false,
        id: "1700039018958",
        type: "string",
        originId: "1700039018958",
      },
      {
        roleType: 0,
        index: 7,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018974,
        location: "dimensions",
        uniqueId: 250529143646390,
        isGeoField: false,
        id: "1700039018974",
        type: "string",
        originId: "1700039018974",
      },
      {
        roleType: 0,
        index: 8,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700042191295,
        location: "dimensions",
        uniqueId: 250529143646429,
        isGeoField: false,
        id: "1700042191295",
        type: "string",
        originId: "1700042191295",
      },
      {
        roleType: 0,
        index: 9,
        format: {
          contentType: "link",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018960,
        location: "dimensions",
        uniqueId: 250529143646571,
        isGeoField: false,
        id: "1700039018960",
        type: "string",
        originId: "1700039018960",
      },
      {
        roleType: 0,
        index: 10,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018959,
        location: "dimensions",
        uniqueId: 250529160631061,
        isGeoField: false,
        type: "string",
        id: "1700039018959",
        originId: "1700039018959",
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: "组合筛选",
        },
        aggrConf: {},
        id: "250529152850177",
        notJoinQuery: false,
        dimMetId: 250529152850177,
        filter: {
          children: [
            {
              roleType: 0,
              disableMenuKey: ["addOrFilter", "subFilter"],
              unremovable: true,
              name: "发布日期",
              format: {},
              aggrConf: {},
              dimMetId: 1700039018955,
              filter: {
                op: "last",
                option: {
                  isReportFilter: false,
                  dateMode: "relative",
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  datetimeUnit: "day",
                  hourSharp: true,
                  anchorOffset: 1,
                },
              },
              preRelation: "and",
              location: "whereList",
              uniqueId: 250529152850220,
              id: "1700039018955",
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: "1700039018955",
              dataSetId: 2213514,
            },
          ],
          op: "and",
        },
        location: "whereList",
        uniqueId: 250529152850177,
        pillType: "combination_filter",
        highlight: false,
        showEditComponent: false,
        nameIndex: 1,
        originId: "250529152850177",
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: "significantDecimal",
            auto: true,
            kSep: true,
            precision: 4,
            type: "digit",
            unit: null,
          },
          dataTypeName: "float",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039148675,
        location: "measures",
        uniqueId: 250529143646476,
        isGeoField: false,
        id: "1700039148675",
        type: "string",
        originId: "1700039148675",
      },
    ],
    periodCompare: [],
    colors: [],
    rows: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    display: {
      type: "table",
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: "#1B1F23",
        headerColor: "#EEF1F5",
        bodyFontSize: 12,
        version: 33,
        pageSize: 10,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: "#FBFBFC",
        headerBackground: true,
        compact: false,
        colSpaceMode: "tight",
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: "solid",
        alignMeasure: "right",
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        headerSubTitleFontBold: false,
        bodyFontColor: "#141414",
        specialValue: {
          measures: "bracketTxt",
          dimensions: "null",
        },
        customFields: {
          enable: true,
        },
        alternateRow: true,
        gridLineHorizontal: true,
        gridLineVertical: true,
        gridLineFrame: true,
        bodyFontItalic: false,
        rowPadding: null,
        gridLineVerticalColor: null,
        tableStyle: "standard",
        rowSpaceMode: "loose",
        gridLineFrameColor: null,
        gridLineVerticalStyle: "solid",
        gridLineHorizontalStyle: "solid",
        headerFontBold: true,
        loadPartialData: false,
        headerFontItalic: false,
        hoverHighlight: "row",
        lineNumber: false,
        gridLineHorizontalColor: null,
        hideHeader: false,
        compactDirection: "horizontal",
        gridLineColor: "#E1E4E8",
        pagination: true,
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: "left",
        bodyFontBold: false,
        transpose: false,
        display: "standard",
      },
      enableAdvisor: true,
      queryType: "table",
    },
    columns: [],
  },
  display: {
    type: "table",
    conf: {
      bodyFontUnderline: false,
      fixedIndex: -1,
      headerFontColor: "#1B1F23",
      headerColor: "#EEF1F5",
      bodyFontSize: 12,
      version: 33,
      pageSize: 10,
      headerFontUnderline: false,
      colPadding: null,
      gridLineFrameWidth: 1,
      headerFontSize: 12,
      headerSubTitleFontSize: 12,
      alternateRowColor: "#FBFBFC",
      headerBackground: true,
      compact: false,
      colSpaceMode: "tight",
      headerSubTitleFontItalic: false,
      gridLineFrameStyle: "solid",
      alignMeasure: "right",
      headerSubTitleFontUnderline: false,
      autoWrap: false,
      gridLineVerticalWidth: 1,
      headerSubTitleFontBold: false,
      bodyFontColor: "#141414",
      specialValue: {
        measures: "bracketTxt",
        dimensions: "null",
      },
      customFields: {
        enable: true,
      },
      alternateRow: true,
      gridLineHorizontal: true,
      gridLineVertical: true,
      gridLineFrame: true,
      bodyFontItalic: false,
      rowPadding: null,
      gridLineVerticalColor: null,
      tableStyle: "standard",
      rowSpaceMode: "loose",
      gridLineFrameColor: null,
      gridLineVerticalStyle: "solid",
      gridLineHorizontalStyle: "solid",
      headerFontBold: true,
      loadPartialData: false,
      headerFontItalic: false,
      hoverHighlight: "row",
      lineNumber: false,
      gridLineHorizontalColor: null,
      hideHeader: false,
      compactDirection: "horizontal",
      gridLineColor: "#E1E4E8",
      pagination: true,
      sortable: false,
      measureFirst: false,
      columnWidth: [],
      headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
      gridLineHorizontalWidth: 1,
      headerMenu: true,
      alignDimension: "left",
      bodyFontBold: false,
      transpose: false,
      display: "standard",
    },
    enableAdvisor: true,
    queryType: "table",
    fieldsFormat: {
      "1700039018961": {
        contentType: "link",
      },
      "1700039042169": {},
      "1700039018977": {},
      "1700039018978": {},
      "1700039018979": {},
      "1700039018980": {},
      "1700039018958": {},
      "1700039018974": {},
      "1700042191295": {},
      "1700039018960": {
        contentType: "link",
      },
      "1700039018959": {},
      "1700039148675": {
        numFormat: {
          precisionType: "significantDecimal",
          auto: true,
          kSep: true,
          precision: 4,
          type: "digit",
          unit: null,
        },
        dataTypeName: "float",
      },
    },
  },
  originalSchema: {
    pagination: {
      queryKey: "619886fc-ef8a-4821-b527-1e45706a0dd6",
      frontOnlyOffset: 10,
      size: 50000,
    },
    cache: {
      enable: true,
      cacheVersion: "V1",
      expire: 300,
    },
    reportFilterConfig: {
      structType: "LeftRight",
      layoutSize: "Normal",
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {
          contentType: "link",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018961,
        location: "dimensions",
        uniqueId: 250529142904924,
        isGeoField: false,
        id: "1700039018961",
        type: "string",
        originId: "1700039018961",
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039042169,
        location: "dimensions",
        uniqueId: 250529143646067,
        isGeoField: false,
        id: "1700039042169",
        type: "string",
        originId: "1700039042169",
      },
      {
        roleType: 0,
        index: 2,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018977,
        location: "dimensions",
        uniqueId: 250529143646124,
        isGeoField: false,
        id: "1700039018977",
        type: "string",
        originId: "1700039018977",
      },
      {
        roleType: 0,
        index: 3,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018978,
        location: "dimensions",
        uniqueId: 250529143646165,
        isGeoField: false,
        id: "1700039018978",
        type: "string",
        originId: "1700039018978",
      },
      {
        roleType: 0,
        index: 4,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018979,
        location: "dimensions",
        uniqueId: 250529143646206,
        isGeoField: false,
        id: "1700039018979",
        type: "string",
        originId: "1700039018979",
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018980,
        location: "dimensions",
        uniqueId: 250529143646247,
        isGeoField: false,
        id: "1700039018980",
        type: "string",
        originId: "1700039018980",
      },
      {
        roleType: 0,
        index: 6,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018958,
        location: "dimensions",
        uniqueId: 250529143646294,
        isGeoField: false,
        id: "1700039018958",
        type: "string",
        originId: "1700039018958",
      },
      {
        roleType: 0,
        index: 7,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018974,
        location: "dimensions",
        uniqueId: 250529143646390,
        isGeoField: false,
        id: "1700039018974",
        type: "string",
        originId: "1700039018974",
      },
      {
        roleType: 0,
        index: 8,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700042191295,
        location: "dimensions",
        uniqueId: 250529143646429,
        isGeoField: false,
        id: "1700042191295",
        type: "string",
        originId: "1700042191295",
      },
      {
        roleType: 0,
        index: 9,
        format: {
          contentType: "link",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018960,
        location: "dimensions",
        uniqueId: 250529143646571,
        isGeoField: false,
        id: "1700039018960",
        type: "string",
        originId: "1700039018960",
      },
      {
        roleType: 0,
        index: 10,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018959,
        location: "dimensions",
        uniqueId: 250529160631061,
        isGeoField: false,
        type: "string",
        id: "1700039018959",
        originId: "1700039018959",
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: "组合筛选",
        },
        aggrConf: {},
        id: "250529152850177",
        notJoinQuery: false,
        dimMetId: 250529152850177,
        filter: {
          children: [
            {
              roleType: 0,
              disableMenuKey: ["addOrFilter", "subFilter"],
              unremovable: true,
              name: "发布日期",
              format: {},
              aggrConf: {},
              dimMetId: 1700039018955,
              filter: {
                op: "last",
                option: {
                  isReportFilter: false,
                  dateMode: "relative",
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  datetimeUnit: "day",
                  hourSharp: true,
                  anchorOffset: 1,
                },
              },
              preRelation: "and",
              location: "whereList",
              uniqueId: 250529152850220,
              id: "1700039018955",
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: "1700039018955",
              dataSetId: 2213514,
            },
          ],
          op: "and",
        },
        location: "whereList",
        uniqueId: 250529152850177,
        pillType: "combination_filter",
        highlight: false,
        showEditComponent: false,
        nameIndex: 1,
        originId: "250529152850177",
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: "significantDecimal",
            auto: true,
            kSep: true,
            precision: 4,
            type: "digit",
            unit: null,
          },
          dataTypeName: "float",
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039148675,
        location: "measures",
        uniqueId: 250529143646476,
        isGeoField: false,
        id: "1700039148675",
        type: "string",
        originId: "1700039148675",
      },
    ],
    periodCompare: [],
    colors: [],
    rows: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    display: {
      type: "table",
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: "#1B1F23",
        headerColor: "#EEF1F5",
        bodyFontSize: 12,
        version: 33,
        pageSize: 10,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: "#FBFBFC",
        headerBackground: true,
        compact: false,
        colSpaceMode: "tight",
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: "solid",
        alignMeasure: "right",
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        headerSubTitleFontBold: false,
        bodyFontColor: "#141414",
        specialValue: {
          measures: "bracketTxt",
          dimensions: "null",
        },
        customFields: {
          enable: true,
        },
        alternateRow: true,
        gridLineHorizontal: true,
        gridLineVertical: true,
        gridLineFrame: true,
        bodyFontItalic: false,
        rowPadding: null,
        gridLineVerticalColor: null,
        tableStyle: "standard",
        rowSpaceMode: "loose",
        gridLineFrameColor: null,
        gridLineVerticalStyle: "solid",
        gridLineHorizontalStyle: "solid",
        headerFontBold: true,
        loadPartialData: false,
        headerFontItalic: false,
        hoverHighlight: "row",
        lineNumber: false,
        gridLineHorizontalColor: null,
        hideHeader: false,
        compactDirection: "horizontal",
        gridLineColor: "#E1E4E8",
        pagination: true,
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: "rgba(20, 20, 20, 0.45)",
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: "left",
        bodyFontBold: false,
        transpose: false,
        display: "standard",
      },
      enableAdvisor: true,
      queryType: "table",
    },
    columns: [],
  },
};
