export const clueTableBody = {
  version: 4,
  metaData: { appId: 1002649 },
  reportId: 52753,
  dataSourceId: 10070,
  query: {
    dataSetId: 2208637,
    dataSetIdList: [2208637],
    fabricBlendingModelInfo: {},
    transform: { type: 'table' },
    groupByIdList: ['1700039134830', '1700038911486'],
    selectIdList: [
      '1700038918023',
      '1700038929670',
      '1700038920775',
      '1700038921079',
    ],
    fillDateTimeList: [],
    locations: {
      dimensions: ['1700039134830', '1700038911486'],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [],
    whereList: [
      {
        name: '留资日期',
        id: '1700038911486',
        preRelation: 'and',
        uniqueId: 250731152325312,
        op: 'last',
        option: {
          isReportFilter: false,
          dateMode: 'relative',
          isWhereInAggr: true,
        },
        val: [1],
        valOption: {
          datetimeUnit: 'month',
          hourSharp: true,
          until: 'yesterday',
          anchorOffset: 0,
        },
      },
      {
        name: '留资收集抖音号',
        id: '1700038911505',
        preRelation: 'and',
        uniqueId: 250801114244061,
        op: 'not like',
        option: { isReportFilter: false, filterPattern: 'Condition' },
        val: ['89020762080'],
        valOption: { fuzzyLikePattern: 'Contain' },
      },
    ],
    periodCompare: [],
    calculation: { trendTable: {} },
    limit: 1000,
    sort: {},
    topN: null,
    paramList: [],
    cache: { enable: true, cacheVersion: 'V1', expire: 300 },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: { isRealMetricQuery: false },
    extendQuery: [],
  },
  schema: {
    rows: [],
    reportFilterConfig: { structType: 'LeftRight', layoutSize: 'Normal' },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: { contentType: 'link' },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039134830,
        location: 'dimensions',
        uniqueId: 250731152325039,
        isGeoField: false,
        type: 'string',
        id: '1700039134830',
        originId: '1700039134830',
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038911486,
        location: 'dimensions',
        uniqueId: 250801160506088,
        isGeoField: false,
        type: 'string',
        id: '1700038911486',
        originId: '1700038911486',
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        name: '留资日期',
        format: {},
        aggrConf: {},
        dataSetId: 2208637,
        dimMetId: 1700038911486,
        filter: {
          op: 'last',
          option: {
            isReportFilter: false,
            dateMode: 'relative',
            isWhereInAggr: true,
          },
          val: [1],
          valOption: {
            datetimeUnit: 'month',
            hourSharp: true,
            until: 'yesterday',
            anchorOffset: 0,
          },
        },
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250731152325312,
        showEditComponent: false,
        isMetric: false,
        id: '1700038911486',
        originId: '1700038911486',
      },
      {
        roleType: 0,
        index: 1,
        name: '留资收集抖音号',
        format: { contentType: 'link' },
        aggrConf: {},
        dimMetId: 1700038911505,
        filter: {
          op: 'not like',
          option: { isReportFilter: false, filterPattern: 'Condition' },
          val: ['89020762080'],
          valOption: { fuzzyLikePattern: 'Contain' },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250801114244061,
        id: '1700038911505',
        showEditComponent: false,
        isMetric: false,
        originId: '1700038911505',
        dataSetId: 2208637,
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038918023,
        location: 'measures',
        uniqueId: 250731152325139,
        isGeoField: false,
        type: 'string',
        id: '1700038918023',
        originId: '1700038918023',
      },
      {
        roleType: 1,
        index: 1,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038929670,
        location: 'measures',
        uniqueId: 250731152325116,
        isGeoField: false,
        type: 'string',
        id: '1700038929670',
        originId: '1700038929670',
      },
      {
        roleType: 1,
        index: 2,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038920775,
        location: 'measures',
        uniqueId: 250731152325162,
        isGeoField: false,
        type: 'string',
        id: '1700038920775',
        originId: '1700038920775',
      },
      {
        roleType: 1,
        index: 3,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038921079,
        location: 'measures',
        uniqueId: 250731152325238,
        isGeoField: false,
        type: 'string',
        id: '1700038921079',
        originId: '1700038921079',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: { measures: 'bracketTxt', dimensions: 'null' },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: { enable: true },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'standard',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: { enable: true, cacheVersion: 'V1', expire: 300 },
    colors: [],
    extensions: { data: {}, list: [], protocolVersion: 1 },
    drill: [],
    tableCalculation: { rules: [] },
    referenceLine: [],
    realMetricTableRouteConfig: { isRealMetricQuery: false },
    subMeasures: [],
    annotation: { hash: 'd751713988987e9331980363e24189ce' },
    columns: [],
  },
  display: {
    conf: {
      bodyFontUnderline: false,
      fixedIndex: -1,
      headerFontColor: '#1B1F23',
      headerColor: '#EEF1F5',
      bodyFontSize: 12,
      pageSize: 20,
      headerFontUnderline: false,
      colPadding: null,
      gridLineFrameWidth: 1,
      specialValue: { measures: 'bracketTxt', dimensions: 'null' },
      headerFontSize: 12,
      headerSubTitleFontSize: 12,
      alternateRowColor: '#FBFBFC',
      headerBackground: true,
      compact: false,
      rowPadding: null,
      headerSubTitleFontItalic: false,
      gridLineFrameStyle: 'solid',
      alignMeasure: 'right',
      headerSubTitleFontUnderline: false,
      autoWrap: false,
      gridLineVerticalWidth: 1,
      version: 33,
      tableStyle: 'standard',
      pagination: false,
      gridLineHorizontalColor: null,
      customFields: { enable: true },
      gridLineHorizontal: true,
      headerFontItalic: false,
      gridLineFrame: true,
      bodyFontItalic: false,
      colSpaceMode: 'tight',
      gridLineVerticalColor: null,
      bodyFontColor: '#141414',
      rowSpaceMode: 'loose',
      gridLineFrameColor: null,
      alternateRow: true,
      gridLineHorizontalStyle: 'solid',
      headerFontBold: true,
      loadPartialData: true,
      transpose: false,
      lineNumber: false,
      hideHeader: false,
      compactDirection: 'horizontal',
      gridLineColor: '#E1E4E8',
      gridLineVerticalStyle: 'solid',
      sortable: false,
      measureFirst: false,
      columnWidth: [],
      headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
      headerSubTitleFontBold: false,
      gridLineHorizontalWidth: 1,
      headerMenu: true,
      alignDimension: 'left',
      hoverHighlight: 'row',
      bodyFontBold: false,
      gridLineVertical: true,
      display: 'standard',
    },
    type: 'table',
    queryType: 'table',
    enableAdvisor: true,
    fieldsFormat: {
      '1700039134830': { contentType: 'link' },
      '1700038911486': {},
      '1700038918023': {
        numFormat: {
          precisionType: 'significantDecimal',
          auto: true,
          kSep: true,
          precision: 4,
          type: 'digit',
          unit: null,
        },
        dataTypeName: 'int',
      },
      '1700038929670': {
        numFormat: {
          precisionType: 'significantDecimal',
          auto: true,
          kSep: true,
          precision: 4,
          type: 'digit',
          unit: null,
        },
        dataTypeName: 'int',
      },
      '1700038920775': {
        numFormat: {
          precisionType: 'significantDecimal',
          auto: true,
          kSep: true,
          precision: 4,
          type: 'digit',
          unit: null,
        },
        dataTypeName: 'int',
      },
      '1700038921079': {
        numFormat: {
          precisionType: 'significantDecimal',
          auto: true,
          kSep: true,
          precision: 4,
          type: 'digit',
          unit: null,
        },
        dataTypeName: 'int',
      },
    },
  },
  originalSchema: {
    rows: [],
    reportFilterConfig: { structType: 'LeftRight', layoutSize: 'Normal' },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: { contentType: 'link' },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039134830,
        location: 'dimensions',
        uniqueId: 250731152325039,
        isGeoField: false,
        type: 'string',
        id: '1700039134830',
        originId: '1700039134830',
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038911486,
        location: 'dimensions',
        uniqueId: 250801160506088,
        isGeoField: false,
        type: 'string',
        id: '1700038911486',
        originId: '1700038911486',
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        name: '留资日期',
        format: {},
        aggrConf: {},
        dataSetId: 2208637,
        dimMetId: 1700038911486,
        filter: {
          op: 'last',
          option: {
            isReportFilter: false,
            dateMode: 'relative',
            isWhereInAggr: true,
          },
          val: [1],
          valOption: {
            datetimeUnit: 'month',
            hourSharp: true,
            until: 'yesterday',
            anchorOffset: 0,
          },
        },
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250731152325312,
        showEditComponent: false,
        isMetric: false,
        id: '1700038911486',
        originId: '1700038911486',
      },
      {
        roleType: 0,
        index: 1,
        name: '留资收集抖音号',
        format: { contentType: 'link' },
        aggrConf: {},
        dimMetId: 1700038911505,
        filter: {
          op: 'not like',
          option: { isReportFilter: false, filterPattern: 'Condition' },
          val: ['89020762080'],
          valOption: { fuzzyLikePattern: 'Contain' },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250801114244061,
        id: '1700038911505',
        showEditComponent: false,
        isMetric: false,
        originId: '1700038911505',
        dataSetId: 2208637,
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038918023,
        location: 'measures',
        uniqueId: 250731152325139,
        isGeoField: false,
        type: 'string',
        id: '1700038918023',
        originId: '1700038918023',
      },
      {
        roleType: 1,
        index: 1,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038929670,
        location: 'measures',
        uniqueId: 250731152325116,
        isGeoField: false,
        type: 'string',
        id: '1700038929670',
        originId: '1700038929670',
      },
      {
        roleType: 1,
        index: 2,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038920775,
        location: 'measures',
        uniqueId: 250731152325162,
        isGeoField: false,
        type: 'string',
        id: '1700038920775',
        originId: '1700038920775',
      },
      {
        roleType: 1,
        index: 3,
        format: {
          numFormat: {
            precisionType: 'significantDecimal',
            auto: true,
            kSep: true,
            precision: 4,
            type: 'digit',
            unit: null,
          },
          dataTypeName: 'int',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700038921079,
        location: 'measures',
        uniqueId: 250731152325238,
        isGeoField: false,
        type: 'string',
        id: '1700038921079',
        originId: '1700038921079',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: { measures: 'bracketTxt', dimensions: 'null' },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: { enable: true },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'standard',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: { enable: true, cacheVersion: 'V1', expire: 300 },
    colors: [],
    extensions: { data: {}, list: [], protocolVersion: 1 },
    drill: [],
    tableCalculation: { rules: [] },
    referenceLine: [],
    realMetricTableRouteConfig: { isRealMetricQuery: false },
    subMeasures: [],
    annotation: { hash: 'd751713988987e9331980363e24189ce' },
    columns: [],
  },
};
