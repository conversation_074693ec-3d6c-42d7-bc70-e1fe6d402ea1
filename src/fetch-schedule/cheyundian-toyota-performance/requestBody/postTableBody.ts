export const postTableBody = {
  version: 4,
  metaData: {
    appId: 1002649,
  },
  reportId: 52633,
  dataSourceId: 10070,
  query: {
    dataSetId: 2213514,
    dataSetIdList: [2213514],
    fabricBlendingModelInfo: {},
    transform: {
      type: 'table',
    },
    groupByIdList: [
      '1700039018971',
      '1700039018958',
      '1700039018970',
      '1700039018960',
      '1700039018961',
      '1700039018955',
      '1700039018974',
    ],
    selectIdList: [
      'sum_1700039018977',
      'sum_1700039042169',
      'sum_1700039018978',
      'sum_1700039018979',
      'sum_1700039018980',
    ],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        '1700039018971',
        '1700039018958',
        '1700039018970',
        '1700039018960',
        '1700039018961',
        '1700039018955',
        '1700039018974',
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [
      {
        id: 'sum_1700039018977',
        originId: '1700039018977',
        dimMetId: 1700039018977,
        uniqueId: 250729203006123,
        name: '播放量',
        expr: 'item_play_count',
        fullExpr: 'item_play_count',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'pengmingguo',
        dataSetId: 2213514,
      },
      {
        id: 'sum_1700039042169',
        originId: '1700039042169',
        dimMetId: 1700039042169,
        uniqueId: 250729203006148,
        name: '消耗',
        expr: 'round(item_cost/100000, 0)',
        fullExpr: 'round(item_cost/100000, 0)',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'pengmingguo',
        dataSetId: 2213514,
      },
      {
        id: 'sum_1700039018978',
        originId: '1700039018978',
        dimMetId: 1700039018978,
        uniqueId: 250729203006173,
        name: '点赞量',
        expr: 'item_like_count',
        fullExpr: 'item_like_count',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'pengmingguo',
        dataSetId: 2213514,
      },
      {
        id: 'sum_1700039018979',
        originId: '1700039018979',
        dimMetId: 1700039018979,
        uniqueId: 250729203006198,
        name: '评论量',
        expr: 'item_comment_count',
        fullExpr: 'item_comment_count',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'pengmingguo',
        dataSetId: 2213514,
      },
      {
        id: 'sum_1700039018980',
        originId: '1700039018980',
        dimMetId: 1700039018980,
        uniqueId: 250729203006223,
        name: '分享量',
        expr: 'item_share_count',
        fullExpr: 'item_share_count',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'pengmingguo',
        dataSetId: 2213514,
      },
    ],
    whereList: [
      {
        nodeType: 1,
        op: 'and',
        val: [
          {
            name: '发布日期',
            id: '1700039018955',
            preRelation: 'and',
            uniqueId: 250729191905104,
            op: 'last',
            option: {
              isReportFilter: false,
              dateMode: 'relative',
              isWhereInAggr: true,
            },
            val: [1],
            valOption: {
              datetimeUnit: 'month',
              hourSharp: true,
              until: 'yesterday',
              anchorOffset: 0,
            },
          },
          {
            name: '数据表现日期',
            id: '1700039018957',
            preRelation: 'and',
            uniqueId: 250729191905105,
            op: 'advancedDate',
            option: {
              isReportFilter: false,
              dateMode: 'advanced',
              isWhereInAggr: true,
            },
            val: null,
            valOption: {
              endTime: {
                datetimeUnit: 'day',
                direction: 'backward',
                type: 'dynamic',
                val: 1,
              },
              startTime: {
                datetimeUnit: 'day',
                direction: 'backward',
                type: 'dynamic',
                val: 30,
              },
            },
          },
        ],
      },
      {
        name: '作者抖音号',
        id: '1700039018960',
        preRelation: 'and',
        uniqueId: 250729200116051,
        op: 'not like',
        option: {
          isReportFilter: false,
          filterPattern: 'Condition',
        },
        val: ['37695094400', '55277644120', '89020762080'],
        valOption: {
          fuzzyLikePattern: 'Contain',
        },
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    rows: [],
    reportFilterConfig: {
      structType: 'LeftRight',
      layoutSize: 'Normal',
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018971,
        location: 'dimensions',
        uniqueId: 250729203006098,
        isGeoField: false,
        type: 'string',
        id: '1700039018971',
        originId: '1700039018971',
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018958,
        location: 'dimensions',
        uniqueId: 250729190809043,
        isGeoField: false,
        type: 'string',
        id: '1700039018958',
        originId: '1700039018958',
      },
      {
        roleType: 0,
        index: 2,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018970,
        location: 'dimensions',
        uniqueId: 250729190809072,
        isGeoField: false,
        type: 'string',
        id: '1700039018970',
        originId: '1700039018970',
      },
      {
        roleType: 0,
        index: 3,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018960,
        location: 'dimensions',
        uniqueId: 250729190809201,
        isGeoField: false,
        type: 'string',
        id: '1700039018960',
        originId: '1700039018960',
      },
      {
        roleType: 0,
        index: 4,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018961,
        location: 'dimensions',
        uniqueId: 250729190809230,
        isGeoField: false,
        type: 'string',
        id: '1700039018961',
        originId: '1700039018961',
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018955,
        location: 'dimensions',
        uniqueId: 250729190809259,
        isGeoField: false,
        type: 'string',
        id: '1700039018955',
        originId: '1700039018955',
      },
      {
        uniqueId: 250730174533061,
        id: '1700039018974',
        location: 'dimensions',
        dimMetId: 1700039018974,
        originId: '1700039018974',
        roleType: 0,
        aggrConf: {},
        format: {},
        isMetric: false,
        index: 6,
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: '组合筛选',
        },
        aggrConf: {},
        notJoinQuery: false,
        dimMetId: 250729191905075,
        filter: {
          children: [
            {
              roleType: 0,
              filter: {
                op: 'last',
                option: {
                  isReportFilter: false,
                  dateMode: 'relative',
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  datetimeUnit: 'month',
                  hourSharp: true,
                  until: 'yesterday',
                  anchorOffset: 0,
                },
              },
              unremovable: true,
              name: '发布日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700039018955,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729191905104,
              id: '1700039018955',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: '1700039018955',
              dataSetId: 2213514,
            },
            {
              roleType: 0,
              filter: {
                op: 'advancedDate',
                option: {
                  isReportFilter: false,
                  dateMode: 'advanced',
                  isWhereInAggr: true,
                },
                val: null,
                valOption: {
                  endTime: {
                    datetimeUnit: 'day',
                    direction: 'backward',
                    type: 'dynamic',
                    val: 1,
                  },
                  startTime: {
                    datetimeUnit: 'day',
                    direction: 'backward',
                    type: 'dynamic',
                    val: 30,
                  },
                },
              },
              unremovable: true,
              name: '数据表现日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700039018957,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729191905105,
              id: '1700039018957',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: '1700039018957',
              dataSetId: 2213514,
            },
          ],
          op: 'and',
        },
        location: 'whereList',
        uniqueId: 250729191905075,
        highlight: false,
        pillType: 'combination_filter',
        showEditComponent: false,
        nameIndex: 1,
        id: '250729191905075',
        originId: '250729191905075',
      },
      {
        roleType: 0,
        index: 1,
        name: '作者抖音号',
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        dataSetId: 2213514,
        dimMetId: 1700039018960,
        filter: {
          op: 'not like',
          option: {
            isReportFilter: false,
            filterPattern: 'Condition',
          },
          val: ['37695094400', '55277644120', '89020762080'],
          valOption: {
            fuzzyLikePattern: 'Contain',
          },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250729200116051,
        showEditComponent: false,
        isMetric: false,
        id: '1700039018960',
        originId: '1700039018960',
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018977,
        location: 'measures',
        uniqueId: 250729203006123,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018977',
        originId: '1700039018977',
      },
      {
        roleType: 1,
        index: 1,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039042169,
        location: 'measures',
        uniqueId: 250729203006148,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039042169',
        originId: '1700039042169',
      },
      {
        roleType: 1,
        index: 2,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018978,
        location: 'measures',
        uniqueId: 250729203006173,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018978',
        originId: '1700039018978',
      },
      {
        roleType: 1,
        index: 3,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018979,
        location: 'measures',
        uniqueId: 250729203006198,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018979',
        originId: '1700039018979',
      },
      {
        roleType: 1,
        index: 4,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018980,
        location: 'measures',
        uniqueId: 250729203006223,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018980',
        originId: '1700039018980',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: {
          measures: 'bracketTxt',
          dimensions: 'null',
        },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: {
          enable: true,
        },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [
          {
            key: '250729190809043',
            value: 164,
          },
          {
            key: '250729190809072',
            value: 450,
          },
          {
            key: '250729190809201',
            value: 107,
          },
          {
            key: '250729190809230',
            value: 168,
          },
          {
            key: '250729190809259',
            value: 100,
          },
          {
            key: '250729190809286',
            value: 64,
          },
          {
            key: '250729190809321',
            value: 52,
          },
          {
            key: '250729190809354',
            value: 64,
          },
          {
            key: '250729190809387',
            value: 64,
          },
          {
            key: '250729190809414',
            value: 64,
          },
        ],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'custom',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    colors: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    columns: [],
  },
  display: {
    conf: {
      bodyFontUnderline: false,
      fixedIndex: -1,
      headerFontColor: '#1B1F23',
      headerColor: '#EEF1F5',
      bodyFontSize: 12,
      pageSize: 20,
      headerFontUnderline: false,
      colPadding: null,
      gridLineFrameWidth: 1,
      specialValue: {
        measures: 'bracketTxt',
        dimensions: 'null',
      },
      headerFontSize: 12,
      headerSubTitleFontSize: 12,
      alternateRowColor: '#FBFBFC',
      headerBackground: true,
      compact: false,
      rowPadding: null,
      headerSubTitleFontItalic: false,
      gridLineFrameStyle: 'solid',
      alignMeasure: 'right',
      headerSubTitleFontUnderline: false,
      autoWrap: false,
      gridLineVerticalWidth: 1,
      version: 33,
      tableStyle: 'standard',
      pagination: false,
      gridLineHorizontalColor: null,
      customFields: {
        enable: true,
      },
      gridLineHorizontal: true,
      headerFontItalic: false,
      gridLineFrame: true,
      bodyFontItalic: false,
      colSpaceMode: 'tight',
      gridLineVerticalColor: null,
      bodyFontColor: '#141414',
      rowSpaceMode: 'loose',
      gridLineFrameColor: null,
      alternateRow: true,
      gridLineHorizontalStyle: 'solid',
      headerFontBold: true,
      loadPartialData: true,
      transpose: false,
      lineNumber: false,
      hideHeader: false,
      compactDirection: 'horizontal',
      gridLineColor: '#E1E4E8',
      gridLineVerticalStyle: 'solid',
      sortable: false,
      measureFirst: false,
      columnWidth: [
        {
          key: '250729190809043',
          value: 164,
        },
        {
          key: '250729190809072',
          value: 450,
        },
        {
          key: '250729190809201',
          value: 107,
        },
        {
          key: '250729190809230',
          value: 168,
        },
        {
          key: '250729190809259',
          value: 100,
        },
        {
          key: '250729190809286',
          value: 64,
        },
        {
          key: '250729190809321',
          value: 52,
        },
        {
          key: '250729190809354',
          value: 64,
        },
        {
          key: '250729190809387',
          value: 64,
        },
        {
          key: '250729190809414',
          value: 64,
        },
      ],
      headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
      headerSubTitleFontBold: false,
      gridLineHorizontalWidth: 1,
      headerMenu: true,
      alignDimension: 'left',
      hoverHighlight: 'row',
      bodyFontBold: false,
      gridLineVertical: true,
      display: 'custom',
    },
    type: 'table',
    queryType: 'table',
    enableAdvisor: true,
    fieldsFormat: {
      '1700039018971': {
        contentType: 'link',
      },
      '1700039018958': {},
      '1700039018970': {
        contentType: 'link',
      },
      '1700039018960': {
        contentType: 'link',
      },
      '1700039018961': {
        contentType: 'link',
      },
      '1700039018955': {},
      '1700039018974': {},
      sum_1700039018977: {},
      sum_1700039042169: {},
      sum_1700039018978: {},
      sum_1700039018979: {},
      sum_1700039018980: {},
    },
  },
  originalSchema: {
    rows: [],
    reportFilterConfig: {
      structType: 'LeftRight',
      layoutSize: 'Normal',
    },
    dimensions: [
      {
        roleType: 0,
        index: 0,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018971,
        location: 'dimensions',
        uniqueId: 250729203006098,
        isGeoField: false,
        type: 'string',
        id: '1700039018971',
        originId: '1700039018971',
      },
      {
        roleType: 0,
        index: 1,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018958,
        location: 'dimensions',
        uniqueId: 250729190809043,
        isGeoField: false,
        type: 'string',
        id: '1700039018958',
        originId: '1700039018958',
      },
      {
        roleType: 0,
        index: 2,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018970,
        location: 'dimensions',
        uniqueId: 250729190809072,
        isGeoField: false,
        type: 'string',
        id: '1700039018970',
        originId: '1700039018970',
      },
      {
        roleType: 0,
        index: 3,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018960,
        location: 'dimensions',
        uniqueId: 250729190809201,
        isGeoField: false,
        type: 'string',
        id: '1700039018960',
        originId: '1700039018960',
      },
      {
        roleType: 0,
        index: 4,
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018961,
        location: 'dimensions',
        uniqueId: 250729190809230,
        isGeoField: false,
        type: 'string',
        id: '1700039018961',
        originId: '1700039018961',
      },
      {
        roleType: 0,
        index: 5,
        format: {},
        aggrConf: {},
        isMetric: false,
        dimMetId: 1700039018955,
        location: 'dimensions',
        uniqueId: 250729190809259,
        isGeoField: false,
        type: 'string',
        id: '1700039018955',
        originId: '1700039018955',
      },
      {
        uniqueId: 250730174533061,
        id: '1700039018974',
        location: 'dimensions',
        dimMetId: 1700039018974,
        originId: '1700039018974',
        roleType: 0,
        aggrConf: {},
        format: {},
        isMetric: false,
        index: 6,
      },
    ],
    parameters: [],
    sizes: [],
    whereList: [
      {
        roleType: 0,
        index: 0,
        unremovable: false,
        format: {
          displayName: '组合筛选',
        },
        aggrConf: {},
        notJoinQuery: false,
        dimMetId: 250729191905075,
        filter: {
          children: [
            {
              roleType: 0,
              filter: {
                op: 'last',
                option: {
                  isReportFilter: false,
                  dateMode: 'relative',
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  datetimeUnit: 'month',
                  hourSharp: true,
                  until: 'yesterday',
                  anchorOffset: 0,
                },
              },
              unremovable: true,
              name: '发布日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700039018955,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729191905104,
              id: '1700039018955',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: '1700039018955',
              dataSetId: 2213514,
            },
            {
              roleType: 0,
              filter: {
                op: 'advancedDate',
                option: {
                  isReportFilter: false,
                  dateMode: 'advanced',
                  isWhereInAggr: true,
                },
                val: null,
                valOption: {
                  endTime: {
                    datetimeUnit: 'day',
                    direction: 'backward',
                    type: 'dynamic',
                    val: 1,
                  },
                  startTime: {
                    datetimeUnit: 'day',
                    direction: 'backward',
                    type: 'dynamic',
                    val: 30,
                  },
                },
              },
              unremovable: true,
              name: '数据表现日期',
              format: {},
              aggrConf: {},
              dimMetId: 1700039018957,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              preRelation: 'and',
              location: 'whereList',
              uniqueId: 250729191905105,
              id: '1700039018957',
              showEditComponent: true,
              inCombinationPill: true,
              isMetric: false,
              originId: '1700039018957',
              dataSetId: 2213514,
            },
          ],
          op: 'and',
        },
        location: 'whereList',
        uniqueId: 250729191905075,
        highlight: false,
        pillType: 'combination_filter',
        showEditComponent: false,
        nameIndex: 1,
        id: '250729191905075',
        originId: '250729191905075',
      },
      {
        roleType: 0,
        index: 1,
        name: '作者抖音号',
        format: {
          contentType: 'link',
        },
        aggrConf: {},
        dataSetId: 2213514,
        dimMetId: 1700039018960,
        filter: {
          op: 'not like',
          option: {
            isReportFilter: false,
            filterPattern: 'Condition',
          },
          val: ['37695094400', '55277644120', '89020762080'],
          valOption: {
            fuzzyLikePattern: 'Contain',
          },
        },
        isRequired: false,
        preRelation: 'and',
        location: 'whereList',
        uniqueId: 250729200116051,
        showEditComponent: false,
        isMetric: false,
        id: '1700039018960',
        originId: '1700039018960',
      },
    ],
    whiteList: [],
    measures: [
      {
        roleType: 1,
        index: 0,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018977,
        location: 'measures',
        uniqueId: 250729203006123,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018977',
        originId: '1700039018977',
      },
      {
        roleType: 1,
        index: 1,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039042169,
        location: 'measures',
        uniqueId: 250729203006148,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039042169',
        originId: '1700039042169',
      },
      {
        roleType: 1,
        index: 2,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018978,
        location: 'measures',
        uniqueId: 250729203006173,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018978',
        originId: '1700039018978',
      },
      {
        roleType: 1,
        index: 3,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018979,
        location: 'measures',
        uniqueId: 250729203006198,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018979',
        originId: '1700039018979',
      },
      {
        roleType: 1,
        index: 4,
        format: {},
        aggrConf: {
          exprAggr: 'sum(',
        },
        isMetric: false,
        dimMetId: 1700039018980,
        location: 'measures',
        uniqueId: 250729203006223,
        isGeoField: false,
        type: 'string',
        id: 'sum_1700039018980',
        originId: '1700039018980',
      },
    ],
    periodCompare: [],
    display: {
      conf: {
        bodyFontUnderline: false,
        fixedIndex: -1,
        headerFontColor: '#1B1F23',
        headerColor: '#EEF1F5',
        bodyFontSize: 12,
        pageSize: 20,
        headerFontUnderline: false,
        colPadding: null,
        gridLineFrameWidth: 1,
        specialValue: {
          measures: 'bracketTxt',
          dimensions: 'null',
        },
        headerFontSize: 12,
        headerSubTitleFontSize: 12,
        alternateRowColor: '#FBFBFC',
        headerBackground: true,
        compact: false,
        rowPadding: null,
        headerSubTitleFontItalic: false,
        gridLineFrameStyle: 'solid',
        alignMeasure: 'right',
        headerSubTitleFontUnderline: false,
        autoWrap: false,
        gridLineVerticalWidth: 1,
        version: 33,
        tableStyle: 'standard',
        pagination: false,
        gridLineHorizontalColor: null,
        customFields: {
          enable: true,
        },
        gridLineHorizontal: true,
        headerFontItalic: false,
        gridLineFrame: true,
        bodyFontItalic: false,
        colSpaceMode: 'tight',
        gridLineVerticalColor: null,
        bodyFontColor: '#141414',
        rowSpaceMode: 'loose',
        gridLineFrameColor: null,
        alternateRow: true,
        gridLineHorizontalStyle: 'solid',
        headerFontBold: true,
        loadPartialData: true,
        transpose: false,
        lineNumber: false,
        hideHeader: false,
        compactDirection: 'horizontal',
        gridLineColor: '#E1E4E8',
        gridLineVerticalStyle: 'solid',
        sortable: false,
        measureFirst: false,
        columnWidth: [
          {
            key: '250729190809043',
            value: 164,
          },
          {
            key: '250729190809072',
            value: 450,
          },
          {
            key: '250729190809201',
            value: 107,
          },
          {
            key: '250729190809230',
            value: 168,
          },
          {
            key: '250729190809259',
            value: 100,
          },
          {
            key: '250729190809286',
            value: 64,
          },
          {
            key: '250729190809321',
            value: 52,
          },
          {
            key: '250729190809354',
            value: 64,
          },
          {
            key: '250729190809387',
            value: 64,
          },
          {
            key: '250729190809414',
            value: 64,
          },
        ],
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontBold: false,
        gridLineHorizontalWidth: 1,
        headerMenu: true,
        alignDimension: 'left',
        hoverHighlight: 'row',
        bodyFontBold: false,
        gridLineVertical: true,
        display: 'custom',
      },
      type: 'table',
      queryType: 'table',
      enableAdvisor: true,
    },
    cache: {
      enable: true,
      cacheVersion: 'V1',
      expire: 300,
    },
    colors: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    drill: [],
    tableCalculation: {
      rules: [],
    },
    referenceLine: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    subMeasures: [],
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    columns: [],
  },
};
