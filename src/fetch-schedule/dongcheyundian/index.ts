import { videoTableBody } from './requestBody/videoTableBody.js';
import { liveTableBody } from './requestBody/liveTableBody.js';
import { getDongCheYunDianCookieByBitable } from '../../utils/bitable.js';
import { sendMessage } from '../../api/webhook.js';
import { importTableData } from '../../utils/import.js';
import { COOKIES, defaultHeaders } from '../../utils/config.js';

async function getToken(cookie: string) {
  const url = 'https://www.autoengine.com/motor/dealer/jdc_saas/aeolus/token';

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        ...defaultHeaders,
        Cookie: cookie,
      },
    });

    const data = await response.json();
    return data.data.token;
  } catch (error) {
    const content = `**同步失败** <at id=all></at>\n\n- 失败原因：懂车云店Cookie 过期，请访问飞书多维表格替换 Cookie \n- https://hj81r4bz4v.feishu.cn/base/UJz5bIBtfaH6k0sPRVRcsUllnIe?table=tbl11OHcJNM5Ime2&view=vewvUyvmaY \n- 打开已经登录巨懂车的浏览器，控制台打开网络，输入过滤出/token接口，获取请求头中的 Cookie 粘贴到表格中，操作步骤在多维表格评论处可参考`;
    await sendMessage('懂车云店数据同步通知', content, 'fail');
  }
}

// 测试环境
// https://new-media-dev.xiaofeilun.cn/new-media-api/ledao/dong-car-cloud-store/short-video/import
// https://new-media-dev.xiaofeilun.cn/new-media-api/ledao/dong-car-cloud-store/live/import

// 正式环境导入接口
const importVideoTableData = (rowsData: any) =>
  importTableData(
    rowsData,
    'https://new-media.xiaofeilun.cn/new-media-api/ledao/dong-car-cloud-store/short-video/import',
    '短视频数据表',
    '懂车云店数据同步通知'
  );
const importLiveTableData = (rowsData: any) =>
  importTableData(
    rowsData,
    'https://new-media.xiaofeilun.cn/new-media-api/ledao/dong-car-cloud-store/live/import',
    '直播数据表',
    '懂车云店数据同步通知'
  );

async function getVideoTableData(token: string) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = videoTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1006461,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
      Cookie: COOKIES.AEOLUS_COOKIE,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const videoTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '***************': 'itemPublishDate',
    '***************': 'reportDate',
    '***************': 'dyUid',
    '***************': 'dyId',
    '***************': 'dyName',
    '***************': 'isEmployeeAccount',
    '***************': 'businessAccountUserId',
    '***************': 'businessAccountAwemeId',
    '***************': 'businessAccountNickname',
    '***************': 'itemIsAdHide',
    '***************': 'itemIsFromAdCreate',
    '***************': 'itemToolType',
    '***************': 'itemId',
    '***************': 'hasToolBinded',
    '***************': 'itemTitle',
    '***************': 'itemDuration',
    '***************': 'itemCreateTime',
    '***************': 'itemPublishTime',
    '***************': 'cost',
    '***************': 'itemPlayCnt',
    '***************': 'itemPlayUcnt',
    '***************': 'itemAnchorShowCnt',
    '***************': 'itemAnchorClickCnt',
    '***************': 'itemComponentShowCnt',
    '***************': 'itemComponentClickCnt',
    '***************': 'itemLikeCnt',
    '***************': 'itemCommentCnt',
    '***************': 'itemShareCnt',
    '***************': 'itemFollowCnt',
    '***************': 'itemAnchorClueCnt',
    '***************': 'feiyuItemClueCnt',
    '***************': 'itemLoadMainpageUcnt',
    '***************': 'itemLoadMessageUcnt',
    '***************': 'itemLoadRoomUcnt',
    '***************': 'itemLoadMessageClueCnt',
    '***************': 'itemPlayAvgTimeUcnt',
  };

  const datasets = videoTableDataOrigin.vizData?.datasets || [];

  const rows = datasets.map((row: any) => {
    const key = Object.keys(row);
    return key.reduce((acc: Record<string, any>, curKey: string) => {
      const mappedKey = columnsKey[curKey as keyof typeof columnsKey];
      if (mappedKey) {
        if (
          [
            'isEmployeeAccount',
            'hasToolBinded',
            'itemIsAdHide',
            'itemIsFromAdCreate',
          ].includes(mappedKey)
        ) {
          acc[mappedKey] = row[curKey] == '是' ? 1 : 0;
        } else {
          acc[mappedKey] = row[curKey];
        }
      }
      return acc;
    }, {});
  });

  return rows;
}

async function getLiveTableData(token: string) {
  const url =
    'https://aeolus.bytedance.com/aeolus/vqs/openApi/v2/vizQuery/query';
  const body = liveTableBody;
  const response = await fetch(url, {
    method: 'POST',
    // @ts-ignore
    headers: {
      ...defaultHeaders,
      'App-Id': 1006461,
      'Content-Language': 'zh-CN',
      'Data-Format-Unit': 'auto',
      'Open-Api-Token': token,
      Cookie: COOKIES.AEOLUS_COOKIE,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  const videoTableDataOrigin = data.data;

  // columnsKey 映射
  const columnsKey = {
    '*************16': 'roomId',
    '*************32': 'liveDuration',
    '250419153425100': 'enterpriseDouyinId',
    '250419153425105': 'enterpriseDouyinName',
    '250419153425114': 'douyinUid',
    '250419153425121': 'liveDouyinId',
    '250419153425156': 'liveDate',
    '250419153425169': 'liveStartTime',
    '250419153425186': 'liveEndTime',
    '250419153425208': 'over25minLiveDuration',
    '250419153425227': 'adSpend',
    '250419153425245': 'componentStatus',
    '250419153425258': 'exposureUcount',
    '250419153425263': 'exposureCount',
    '250419153425275': 'viewCount',
    '250419153425284': 'exposureEntryRate',
    '250419153425297': 'peakConcurrentUsers',
    '250419153425300': 'avgConcurrentUsers',
    '250419153425309': 'avgStayDuration',
    '250419153425318': 'newFollowerCount',
    '250419153425327': 'fansGroupJoinCount',
    '250419153425336': 'interactionUcount',
    '250419153425339': 'interactionCount',
    '250419153425358': 'shareUcount',
    '250419153425365': 'shareCount',
    '250419153425374': 'diggUcount',
    '250419153425377': 'diggCount',
    '250419153425390': 'commentUcount',
    '250419153425393': 'commentCount',
    '250419153425402': 'componentClickCount',
    '250419153425411': 'privateMessageUcount',
    '250419153425420': 'totalLeadsUcount',
    '250419153425429': 'naturalLeadsUcount',
    '250419153425438': 'adLeadsUcount',
    '250419165422019': 'liveDouyinName',
    '250421153013019': 'viewUcount',
  };

  const datasets = videoTableDataOrigin.vizData?.datasets || [];

  const rows = datasets.map((row: any) => {
    const key = Object.keys(row);
    return key.reduce((acc: Record<string, any>, curKey: string) => {
      const mappedKey = columnsKey[curKey as keyof typeof columnsKey];
      if (mappedKey) {
        if (mappedKey === 'componentStatus') {
          acc[mappedKey] = row[curKey] === '是' ? 1 : 0;
        } else {
          acc[mappedKey] = row[curKey];
        }
      }
      return acc;
    }, {} as Record<string, any>);
  });
  return rows;
}

export async function dongcheyundianDataSync() {
  // 1. 从多维表格中的先获取车云店Cookie
  const dongCheYunDianCookie = await getDongCheYunDianCookieByBitable();
  // 2. 请求第一个接口获取请求接口需要的鉴权 Token
  const dongCheYunDianToken = await getToken(dongCheYunDianCookie);
  // 使用 Token 来获取数据，发送给后端同步数据
  const videoRowsData = await getVideoTableData(dongCheYunDianToken);
  const liveRowsData = await getLiveTableData(dongCheYunDianToken);
  await Promise.all([
    importVideoTableData(videoRowsData),
    importLiveTableData(liveRowsData),
  ]);
}
