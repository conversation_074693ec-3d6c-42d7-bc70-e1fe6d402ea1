export const liveTableBody = {
  version: 4,
  metaData: {
    appId: 1006461,
  },
  reportId: 1406,
  dataSourceId: 10178,
  query: {
    dataSetId: 3096150,
    dataSetIdList: [3096150],
    fabricBlendingModelInfo: {},
    transform: {
      type: 'table',
    },
    groupByIdList: [
      '1700057553605',
      '1700057553648',
      '1700057553649',
      '1700057553604',
      '1700057553643',
      '1700057553644',
      '1700057553624',
      '1700057553606',
      '1700057553607',
      '1700057553629',
    ],
    selectIdList: [
      'sum_1700057553656',
      'sum_1700057553663',
      'sum_1700057553623',
      'sum_1700057553611',
      'sum_1700057553610',
      'sum_1700057553630',
      'sum_1700057553634',
      'sum_1700057553664',
      'sum_1700057553609',
      'sum_1700057553665',
      'sum_1700057553661',
      'sum_1700057553617',
      'sum_1700057553625',
      'sum_1700057553619',
      'sum_1700057553620',
      'sum_1700057553615',
      'sum_1700057553616',
      'sum_1700057553614',
      'sum_1700057553631',
      'sum_1700057553612',
      'sum_1700057553613',
      'sum_1700057553640',
      'sum_1700057553647',
      'sum_1700057553626',
      'sum_1700057553653',
      'sum_1700057554540',
    ],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        '1700057553605',
        '1700057553648',
        '1700057553649',
        '1700057553604',
        '1700057553643',
        '1700057553644',
        '1700057553624',
        '1700057553606',
        '1700057553607',
        '1700057553629',
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [
      {
        id: 'sum_1700057553656',
        originId: '1700057553656',
        dimMetId: 1700057553656,
        uniqueId: 250419153425032,
        name: '^直^播^时^长(^分)',
        expr: 'live_duration/60',
        fullExpr: 'live_duration/60',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553663',
        originId: '1700057553663',
        dimMetId: 1700057553663,
        uniqueId: 250419153425208,
        name: '^超25^分^钟^直^播^时^长(^分)',
        expr: 'if(live_duration >= 25*60, live_duration, 0) / 60',
        fullExpr: 'if(live_duration >= 25*60, live_duration, 0) / 60',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553623',
        originId: '1700057553623',
        dimMetId: 1700057553623,
        uniqueId: 250419153425227,
        name: '^消^耗^，^单^位^：^元',
        expr: 'round(cost/100000, 2)',
        fullExpr: 'round(cost/100000, 2)',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553611',
        originId: '1700057553611',
        dimMetId: 1700057553611,
        uniqueId: 250419153425258,
        name: '^曝^光^人^数',
        expr: '`live_show_uv_by_room`',
        fullExpr: '`live_show_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553610',
        originId: '1700057553610',
        dimMetId: 1700057553610,
        uniqueId: 250419153425263,
        name: '^曝^光^次^数',
        expr: '`live_show_count`',
        fullExpr: '`live_show_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553630',
        originId: '1700057553630',
        dimMetId: 1700057553630,
        uniqueId: 250421153013019,
        name: '^场^观',
        expr: '`live_watch_uv_by_room_server`',
        fullExpr: '`live_watch_uv_by_room_server`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553634',
        originId: '1700057553634',
        dimMetId: 1700057553634,
        uniqueId: 250419153425275,
        name: '^观^看^次^数',
        expr: '`live_watch_count_server`',
        fullExpr: '`live_watch_count_server`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553664',
        originId: '1700057553664',
        dimMetId: 1700057553664,
        uniqueId: 250419153425284,
        name: '^曝^光^进^入^率',
        expr: 'live_watch_uv_by_room_server/live_show_uv_by_room',
        fullExpr: 'live_watch_uv_by_room_server/live_show_uv_by_room',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553609',
        originId: '1700057553609',
        dimMetId: 1700057553609,
        uniqueId: 250419153425297,
        name: '^最^高^在^线^人^数',
        expr: '`live_minute_max_watch_uv`',
        fullExpr: '`live_minute_max_watch_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553665',
        originId: '1700057553665',
        dimMetId: 1700057553665,
        uniqueId: 250419153425300,
        name: '^平^均^在^线^人^数',
        expr: 'round(live_total_minute_max_watch_uv * 60 / live_duration,0)',
        fullExpr:
          'round(live_total_minute_max_watch_uv * 60 / live_duration,0)',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553661',
        originId: '1700057553661',
        dimMetId: 1700057553661,
        uniqueId: 250419153425309,
        name: '^停^留(^分)',
        expr: '`live_watch_duration_pavg`/60',
        fullExpr: '`live_watch_duration_pavg`/60',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553617',
        originId: '1700057553617',
        dimMetId: 1700057553617,
        uniqueId: 250419153425318,
        name: '^涨^粉^量',
        expr: '`live_follow_uv_by_room`',
        fullExpr: '`live_follow_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553625',
        originId: '1700057553625',
        dimMetId: 1700057553625,
        uniqueId: 250419153425327,
        name: '^加^团^人^数',
        expr: '`live_fans_club_join_uv_by_room`',
        fullExpr: '`live_fans_club_join_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553619',
        originId: '1700057553619',
        dimMetId: 1700057553619,
        uniqueId: 250419153425336,
        name: '^互^动^人^数',
        expr: '`live_interact_uv_by_room`',
        fullExpr: '`live_interact_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553620',
        originId: '1700057553620',
        dimMetId: 1700057553620,
        uniqueId: 250419153425339,
        name: '^互^动^次^数',
        expr: '`live_interact_count`',
        fullExpr: '`live_interact_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553615',
        originId: '1700057553615',
        dimMetId: 1700057553615,
        uniqueId: 250419153425358,
        name: '^分^享^人^数',
        expr: '`live_share_uv_by_room`',
        fullExpr: '`live_share_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553616',
        originId: '1700057553616',
        dimMetId: 1700057553616,
        uniqueId: 250419153425365,
        name: '^分^享^次^数',
        expr: '`live_share_count`',
        fullExpr: '`live_share_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553614',
        originId: '1700057553614',
        dimMetId: 1700057553614,
        uniqueId: 250419153425374,
        name: '^点^赞^人^数',
        expr: '`live_like_uv_by_room`',
        fullExpr: '`live_like_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553631',
        originId: '1700057553631',
        dimMetId: 1700057553631,
        uniqueId: 250419153425377,
        name: '^点^赞^次^数',
        expr: '`live_like_count_server`',
        fullExpr: '`live_like_count_server`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553612',
        originId: '1700057553612',
        dimMetId: 1700057553612,
        uniqueId: 250419153425390,
        name: '^评^论^人^数',
        expr: '`live_comment_uv_by_room`',
        fullExpr: '`live_comment_uv_by_room`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553613',
        originId: '1700057553613',
        dimMetId: 1700057553613,
        uniqueId: 250419153425393,
        name: '^评^论^次^数',
        expr: '`live_comment_count`',
        fullExpr: '`live_comment_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553640',
        originId: '1700057553640',
        dimMetId: 1700057553640,
        uniqueId: 250419153425402,
        name: '^小^风^车^点^击^次^数(^不^含^小^雪^花)',
        expr: '`live_icon_click_count`',
        fullExpr: '`live_icon_click_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553647',
        originId: '1700057553647',
        dimMetId: 1700057553647,
        uniqueId: 250419153425411,
        name: '^私^信^人^数',
        expr: '`im_message_conversation_count`',
        fullExpr: '`im_message_conversation_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553626',
        originId: '1700057553626',
        dimMetId: 1700057553626,
        uniqueId: 250419153425420,
        name: '^全^场^景^线^索^人^数',
        expr: '`live_period_feiyu_total_clue_uv`',
        fullExpr: '`live_period_feiyu_total_clue_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057553653',
        originId: '1700057553653',
        dimMetId: 1700057553653,
        uniqueId: 250419153425429,
        name: '^自^然^流^线^索^人^数',
        expr: '`live_period_feiyu_nature_clue_uv`',
        fullExpr: '`live_period_feiyu_nature_clue_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
      {
        id: 'sum_1700057554540',
        originId: '1700057554540',
        dimMetId: 1700057554540,
        uniqueId: 250419153425438,
        name: '^广^告^流^线^索^人^数',
        expr: 'live_period_feiyu_total_clue_uv - live_period_feiyu_nature_clue_uv',
        fullExpr:
          'live_period_feiyu_total_clue_uv - live_period_feiyu_nature_clue_uv',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'gaoronglei',
        dataSetId: 3096150,
      },
    ],
    whereList: [
      {
        name: 'p_date',
        id: '1700057501789',
        preRelation: 'and',
        uniqueId: 250419153425009,
        op: 'advancedDate',
        option: {
          dateMode: 'advanced',
          isDefaultPartitionField: true,
          isReportFilter: false,
          isWhereInAggr: true,
        },
        val: null,
        valOption: {
          endTime: {
            datetimeUnit: 'day',
            direction: 'backward',
            type: 'dynamic',
            val: 1,
          },
          startTime: {
            datetimeUnit: 'day',
            direction: 'backward',
            type: 'dynamic',
            val: 3,
          },
        },
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    pagination: {
      frontOnlyOffset: 20,
      queryKey: '46904214-5f91-4449-af83-b5f72830b6cf',
      size: 50000,
      offset: 0,
    },
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700057553605,
        format: {},
        id: '1700057553605',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553605',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425016,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553648,
        format: {
          contentType: 'link',
        },
        id: '1700057553648',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553648',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425100,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553649,
        format: {
          contentType: 'link',
        },
        id: '1700057553649',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553649',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425105,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553604,
        format: {},
        id: '1700057553604',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553604',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425114,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553643,
        format: {
          contentType: 'link',
        },
        id: '1700057553643',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553643',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425121,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553644,
        format: {
          contentType: 'link',
        },
        id: '1700057553644',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553644',
        roleType: 0,
        type: 'string',
        uniqueId: 250419165422019,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553624,
        format: {},
        id: '1700057553624',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553624',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425156,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553606,
        format: {},
        id: '1700057553606',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553606',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425169,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553607,
        format: {},
        id: '1700057553607',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553607',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425186,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553629,
        format: {
          contentType: 'link',
        },
        id: '1700057553629',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553629',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425245,
      },
    ],
    display: {
      conf: {
        alignDimension: 'left',
        alignMeasure: 'right',
        alternateRow: true,
        alternateRowColor: '^#FBFBFC',
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: '^#141414',
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: 'tight',
        columnWidth: [],
        compact: false,
        compactDirection: 'horizontal',
        customFields: {
          enable: true,
        },
        display: 'standard',
        fixedIndex: -1,
        gridLineColor: '^#E1E4E8',
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: 'solid',
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: 'solid',
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: 'solid',
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: '^#EEF1F5',
        headerFontBold: true,
        headerFontColor: '^#1B1F23',
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: 'row',
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 20,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: 'loose',
        sortable: false,
        specialValue: {
          dimensions: 'null',
          measures: 'bracketTxt',
        },
        tableStyle: 'standard',
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: 'table',
      type: 'table',
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553656,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553656',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553656',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425032,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553663,
        format: {
          dataTypeName: 'float',
        },
        id: 'sum_1700057553663',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553663',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425208,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553623,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553623',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553623',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425227,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553611,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553611',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553611',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425258,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553610,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553610',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553610',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425263,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553630,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553630',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553630',
        roleType: 1,
        type: 'string',
        uniqueId: 250421153013019,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553634,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553634',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553634',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425275,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553664,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553664',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553664',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425284,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553609,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553609',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553609',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425297,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553665,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553665',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553665',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425300,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553661,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553661',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553661',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425309,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553617,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553617',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553617',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425318,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553625,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553625',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553625',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425327,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553619,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553619',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553619',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425336,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553620,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553620',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553620',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425339,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553615,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553615',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553615',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425358,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553616,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553616',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553616',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425365,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553614,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553614',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553614',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425374,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553631,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553631',
        index: 18,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553631',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425377,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553612,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553612',
        index: 19,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553612',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425390,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553613,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553613',
        index: 20,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553613',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425393,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553640,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553640',
        index: 21,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553640',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425402,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553647,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553647',
        index: 22,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553647',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425411,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553626,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553626',
        index: 23,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553626',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425420,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553653,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553653',
        index: 24,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553653',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425429,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057554540,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057554540',
        index: 25,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057554540',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425438,
      },
    ],
    pagination: {
      frontOnlyOffset: 20,
      queryKey: '46904214-5f91-4449-af83-b5f72830b6cf',
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: 'Normal',
      structType: 'LeftRight',
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dataSetId: 3096150,
        dataTypeName: 'date',
        dimMetId: 1700057501789,
        filter: {
          op: 'advancedDate',
          option: {
            dateMode: 'advanced',
            isDefaultPartitionField: true,
            isReportFilter: false,
            isWhereInAggr: true,
          },
          val: null,
          valOption: {
            endTime: {
              datetimeUnit: 'day',
              direction: 'backward',
              type: 'dynamic',
              val: 1,
            },
            startTime: {
              datetimeUnit: 'day',
              direction: 'backward',
              type: 'dynamic',
              val: 3,
            },
          },
        },
        format: {},
        highlight: false,
        id: '1700057501789',
        index: 0,
        location: 'whereList',
        name: 'p_date',
        originId: '1700057501789',
        preRelation: 'and',
        roleType: 0,
        showEditComponent: false,
        undraggable: false,
        uniqueId: 250419153425009,
        unremovable: true,
      },
    ],
    whiteList: [],
  },
  display: {
    conf: {
      alignDimension: 'left',
      alignMeasure: 'right',
      alternateRow: true,
      alternateRowColor: '^#FBFBFC',
      autoWrap: false,
      bodyFontBold: false,
      bodyFontColor: '^#141414',
      bodyFontItalic: false,
      bodyFontSize: 12,
      bodyFontUnderline: false,
      colPadding: null,
      colSpaceMode: 'tight',
      columnWidth: [],
      compact: false,
      compactDirection: 'horizontal',
      customFields: {
        enable: true,
      },
      display: 'standard',
      fixedIndex: -1,
      gridLineColor: '^#E1E4E8',
      gridLineFrame: true,
      gridLineFrameColor: null,
      gridLineFrameStyle: 'solid',
      gridLineFrameWidth: 1,
      gridLineHorizontal: true,
      gridLineHorizontalColor: null,
      gridLineHorizontalStyle: 'solid',
      gridLineHorizontalWidth: 1,
      gridLineVertical: true,
      gridLineVerticalColor: null,
      gridLineVerticalStyle: 'solid',
      gridLineVerticalWidth: 1,
      headerBackground: true,
      headerColor: '^#EEF1F5',
      headerFontBold: true,
      headerFontColor: '^#1B1F23',
      headerFontItalic: false,
      headerFontSize: 12,
      headerFontUnderline: false,
      headerMenu: true,
      headerSubTitleFontBold: false,
      headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
      headerSubTitleFontItalic: false,
      headerSubTitleFontSize: 12,
      headerSubTitleFontUnderline: false,
      hideHeader: false,
      hoverHighlight: 'row',
      lineNumber: false,
      loadPartialData: false,
      measureFirst: false,
      pageSize: 20,
      pagination: true,
      rowPadding: null,
      rowSpaceMode: 'loose',
      sortable: false,
      specialValue: {
        dimensions: 'null',
        measures: 'bracketTxt',
      },
      tableStyle: 'standard',
      transpose: false,
      version: 33,
    },
    enableAdvisor: true,
    queryType: 'table',
    type: 'table',
    fieldsFormat: {
      '1700057553605': {},
      '1700057553648': {
        contentType: 'link',
      },
      '1700057553649': {
        contentType: 'link',
      },
      '1700057553604': {},
      '1700057553643': {
        contentType: 'link',
      },
      '1700057553644': {
        contentType: 'link',
      },
      '1700057553624': {},
      '1700057553606': {},
      '1700057553607': {},
      '1700057553629': {
        contentType: 'link',
      },
      sum_1700057553656: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553663: {
        dataTypeName: 'float',
      },
      sum_1700057553623: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553611: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553610: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553630: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553634: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553664: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553609: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553665: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553661: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553617: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553625: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553619: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553620: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553615: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553616: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553614: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553631: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553612: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553613: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553640: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553647: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553626: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057553653: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057554540: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
    },
  },
  originalSchema: {
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700057553605,
        format: {},
        id: '1700057553605',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553605',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425016,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553648,
        format: {
          contentType: 'link',
        },
        id: '1700057553648',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553648',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425100,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553649,
        format: {
          contentType: 'link',
        },
        id: '1700057553649',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553649',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425105,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553604,
        format: {},
        id: '1700057553604',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553604',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425114,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553643,
        format: {
          contentType: 'link',
        },
        id: '1700057553643',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553643',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425121,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553644,
        format: {
          contentType: 'link',
        },
        id: '1700057553644',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553644',
        roleType: 0,
        type: 'string',
        uniqueId: 250419165422019,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553624,
        format: {},
        id: '1700057553624',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553624',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425156,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553606,
        format: {},
        id: '1700057553606',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553606',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425169,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553607,
        format: {},
        id: '1700057553607',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553607',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425186,
      },
      {
        aggrConf: {},
        dimMetId: 1700057553629,
        format: {
          contentType: 'link',
        },
        id: '1700057553629',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057553629',
        roleType: 0,
        type: 'string',
        uniqueId: 250419153425245,
      },
    ],
    display: {
      conf: {
        alignDimension: 'left',
        alignMeasure: 'right',
        alternateRow: true,
        alternateRowColor: '^#FBFBFC',
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: '^#141414',
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: 'tight',
        columnWidth: [],
        compact: false,
        compactDirection: 'horizontal',
        customFields: {
          enable: true,
        },
        display: 'standard',
        fixedIndex: -1,
        gridLineColor: '^#E1E4E8',
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: 'solid',
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: 'solid',
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: 'solid',
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: '^#EEF1F5',
        headerFontBold: true,
        headerFontColor: '^#1B1F23',
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: 'row',
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 20,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: 'loose',
        sortable: false,
        specialValue: {
          dimensions: 'null',
          measures: 'bracketTxt',
        },
        tableStyle: 'standard',
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: 'table',
      type: 'table',
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553656,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553656',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553656',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425032,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553663,
        format: {
          dataTypeName: 'float',
        },
        id: 'sum_1700057553663',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553663',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425208,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553623,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553623',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553623',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425227,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553611,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553611',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553611',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425258,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553610,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553610',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553610',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425263,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553630,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553630',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553630',
        roleType: 1,
        type: 'string',
        uniqueId: 250421153013019,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553634,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553634',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553634',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425275,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553664,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553664',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553664',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425284,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553609,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553609',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553609',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425297,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553665,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553665',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553665',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425300,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553661,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553661',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553661',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425309,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553617,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553617',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553617',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425318,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553625,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553625',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553625',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425327,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553619,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553619',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553619',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425336,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553620,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553620',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553620',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425339,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553615,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553615',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553615',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425358,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553616,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553616',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553616',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425365,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553614,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553614',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553614',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425374,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553631,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553631',
        index: 18,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553631',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425377,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553612,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553612',
        index: 19,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553612',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425390,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553613,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553613',
        index: 20,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553613',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425393,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553640,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553640',
        index: 21,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553640',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425402,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553647,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553647',
        index: 22,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553647',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425411,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553626,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553626',
        index: 23,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553626',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425420,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057553653,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057553653',
        index: 24,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057553653',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425429,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057554540,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057554540',
        index: 25,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057554540',
        roleType: 1,
        type: 'string',
        uniqueId: 250419153425438,
      },
    ],
    pagination: {
      frontOnlyOffset: 20,
      queryKey: '46904214-5f91-4449-af83-b5f72830b6cf',
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: 'Normal',
      structType: 'LeftRight',
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dataSetId: 3096150,
        dataTypeName: 'date',
        dimMetId: 1700057501789,
        filter: {
          op: 'advancedDate',
          option: {
            dateMode: 'advanced',
            isDefaultPartitionField: true,
            isReportFilter: false,
            isWhereInAggr: true,
          },
          val: null,
          valOption: {
            endTime: {
              datetimeUnit: 'day',
              direction: 'backward',
              type: 'dynamic',
              val: 1,
            },
            startTime: {
              datetimeUnit: 'day',
              direction: 'backward',
              type: 'dynamic',
              val: 3,
            },
          },
        },
        format: {},
        highlight: false,
        id: '1700057501789',
        index: 0,
        location: 'whereList',
        name: 'p_date',
        originId: '1700057501789',
        preRelation: 'and',
        roleType: 0,
        showEditComponent: false,
        undraggable: false,
        uniqueId: 250419153425009,
        unremovable: true,
      },
    ],
    whiteList: [],
  },
};
