export const videoTableBody = {
  version: 4,
  metaData: {
    appId: 1006461,
  },
  reportId: 2003,
  dataSourceId: 10178,
  query: {
    dataSetId: 3096130,
    dataSetIdList: [3096130],
    fabricBlendingModelInfo: {},
    transform: {
      type: 'table',
    },
    groupByIdList: [
      '1700057501191',
      '1700057501192',
      '1700057501193',
      '1700057501194',
      '1700057501195',
      '1700057501196',
      '1700057501189',
      '1700057501229',
      '1700057501228',
      '1700057815728',
      '1700057501211',
      '1700057815729',
      '1700057501203',
      '1700057501225',
      '1700057501205',
      '1700057501207',
      '1700057501208',
      '1700057501209',
    ],
    selectIdList: [
      'sum_1700057501226',
      'sum_1700057501212',
      'sum_1700057501230',
      'sum_1700057501220',
      'sum_1700057501221',
      'sum_1700057501222',
      'sum_1700057501223',
      'sum_1700057501213',
      'sum_1700057501214',
      'sum_1700057501215',
      'sum_1700057501218',
      'sum_1700057501224',
      'sum_1700057501227',
      'sum_1700057501234',
      'sum_1700057501237',
      'sum_1700057501236',
      'sum_1700057501238',
      '1700057815734',
    ],
    fillDateTimeList: [],
    locations: {
      dimensions: [
        '1700057501191',
        '1700057501192',
        '1700057501193',
        '1700057501194',
        '1700057501195',
        '1700057501196',
        '1700057501189',
        '1700057501229',
        '1700057501228',
        '1700057815728',
        '1700057501211',
        '1700057815729',
        '1700057501203',
        '1700057501225',
        '1700057501205',
        '1700057501207',
        '1700057501208',
        '1700057501209',
      ],
      rows: [],
      columns: [],
      tooltips: [],
    },
    dimMetList: [
      {
        id: 'sum_1700057501226',
        originId: '1700057501226',
        dimMetId: 1700057501226,
        uniqueId: 250509180454665,
        name: '^短^视^频^广^告^消^耗',
        expr: '`item_cost`/100000',
        fullExpr: '`item_cost`/100000',
        roleType: 1,
        scope: 0,
        dataType: 'float',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501212',
        originId: '1700057501212',
        dimMetId: 1700057501212,
        uniqueId: 250509180454694,
        name: '^播^放^量',
        expr: '`item_play_count`',
        fullExpr: '`item_play_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501230',
        originId: '1700057501230',
        dimMetId: 1700057501230,
        uniqueId: 250509180454721,
        name: '^短^视^频^播^放^人^数',
        expr: '`item_play_uv`',
        fullExpr: '`item_play_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501220',
        originId: '1700057501220',
        dimMetId: 1700057501220,
        uniqueId: 250509180454748,
        name: '^锚^点^曝^光^量',
        expr: '`item_anchor_show_count`',
        fullExpr: '`item_anchor_show_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501221',
        originId: '1700057501221',
        dimMetId: 1700057501221,
        uniqueId: 250509180454773,
        name: '^锚^点^点^击^量',
        expr: '`item_anchor_click_count`',
        fullExpr: '`item_anchor_click_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501222',
        originId: '1700057501222',
        dimMetId: 1700057501222,
        uniqueId: 250509180454802,
        name: '^短^视^频^评^论^区link^曝^光^量',
        expr: '`item_component_show_count`',
        fullExpr: '`item_component_show_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501223',
        originId: '1700057501223',
        dimMetId: 1700057501223,
        uniqueId: 250509180454827,
        name: '^短^视^频^评^论^区link^点^击^量',
        expr: '`item_component_click_count`',
        fullExpr: '`item_component_click_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501213',
        originId: '1700057501213',
        dimMetId: 1700057501213,
        uniqueId: 250509180454856,
        name: '^点^赞^数',
        expr: '`item_like_count`',
        fullExpr: '`item_like_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501214',
        originId: '1700057501214',
        dimMetId: 1700057501214,
        uniqueId: 250509180454885,
        name: '^评^论^数',
        expr: '`item_comment_count`',
        fullExpr: '`item_comment_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501215',
        originId: '1700057501215',
        dimMetId: 1700057501215,
        uniqueId: 250509180454918,
        name: '^分^享^数',
        expr: '`item_share_count`',
        fullExpr: '`item_share_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501218',
        originId: '1700057501218',
        dimMetId: 1700057501218,
        uniqueId: 250509180454955,
        name: '^短^视^频^涨^粉^数',
        expr: '`item_follow_count`',
        fullExpr: '`item_follow_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501224',
        originId: '1700057501224',
        dimMetId: 1700057501224,
        uniqueId: 250509180454986,
        name: '^锚^点^表^单^留^资^次^数',
        expr: '`item_anchor_clue_count`',
        fullExpr: '`item_anchor_clue_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501227',
        originId: '1700057501227',
        dimMetId: 1700057501227,
        uniqueId: 250509180455011,
        name: '^总^表^单^留^资^次^数',
        expr: '`feiyu_item_clue_count`',
        fullExpr: '`feiyu_item_clue_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501234',
        originId: '1700057501234',
        dimMetId: 1700057501234,
        uniqueId: 250509180455044,
        name: '^引^导^进^入^主^页^人^数',
        expr: '`item_load_mainpage_uv`',
        fullExpr: '`item_load_mainpage_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501237',
        originId: '1700057501237',
        dimMetId: 1700057501237,
        uniqueId: 250509180455069,
        name: '^引^导^私^信^人^数',
        expr: '`item_load_message_uv`',
        fullExpr: '`item_load_message_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501236',
        originId: '1700057501236',
        dimMetId: 1700057501236,
        uniqueId: 250509180455094,
        name: '^引^导^直^播^间^人^数',
        expr: '`item_load_room_uv`',
        fullExpr: '`item_load_room_uv`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
      {
        id: 'sum_1700057501238',
        originId: '1700057501238',
        dimMetId: 1700057501238,
        uniqueId: 250509180455119,
        name: '^短^视^频^引^导^私^信^线^索^量^（^飞^鱼^线^索^归^因^）',
        expr: '`item_load_message_clue_count`',
        fullExpr: '`item_load_message_clue_count`',
        roleType: 1,
        scope: 0,
        dataType: 'int',
        isRaw: false,
        mapKey: null,
        aggregation: {
          exprAggr: 'sum(',
        },
        sourceType: 'aggr',
        persisted: false,
        ownerEmailPrefix: 'songzikang.sumiya',
        dataSetId: 3096130,
      },
    ],
    whereList: [
      {
        name: 'p_date[app_store_enterprise_short_video_bi_core_df]',
        id: '1700057501188',
        preRelation: 'and',
        uniqueId: 250509180454011,
        op: 'lastSync',
        option: {
          dateMode: 'relative',
          isDefaultPartitionField: true,
          isReportFilter: false,
          isWhereInAggr: true,
        },
        val: [1],
        valOption: {
          anchorOffset: 0,
          datetimeUnit: 'day',
          hourSharp: true,
        },
      },
      {
        nodeType: 1,
        op: 'and',
        val: [
          {
            name: '^短^视^频^发^布^日^期',
            id: '1700057501191',
            preRelation: 'and',
            uniqueId: 250509180455351,
            op: 'last',
            option: {
              dateMode: 'relative',
              isReportFilter: false,
              isWhereInAggr: true,
            },
            val: [30],
            valOption: {
              anchorOffset: 1,
              datetimeUnit: 'day',
              hourSharp: true,
            },
          },
          {
            name: '^数^据^表^现^日^期',
            id: '1700057501192',
            preRelation: 'and',
            uniqueId: 250509180455352,
            op: 'last',
            option: {
              dateMode: 'relative',
              isReportFilter: false,
              isWhereInAggr: true,
            },
            val: [1],
            valOption: {
              anchorOffset: 1,
              datetimeUnit: 'day',
              hourSharp: true,
            },
          },
        ],
      },
    ],
    periodCompare: [],
    calculation: {
      trendTable: {},
    },
    limit: 1000,
    pagination: {
      frontOnlyOffset: 20,
      queryKey: 'f2bb757d-4603-4eb6-9677-8573f9fa3e68',
      size: 50000,
      offset: 0,
    },
    sort: {},
    topN: null,
    paramList: [],
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    enableNullJoin: false,
    hasDynamicField: false,
    isFirstScreen: false,
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    extendQuery: [],
  },
  schema: {
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700057501191,
        format: {},
        id: '1700057501191',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501191',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454042,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501192,
        format: {},
        id: '1700057501192',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501192',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454077,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501193,
        format: {},
        id: '1700057501193',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501193',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454283,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501194,
        format: {
          contentType: 'link',
        },
        id: '1700057501194',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501194',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454153,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501195,
        format: {
          contentType: 'link',
        },
        id: '1700057501195',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501195',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454178,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501196,
        format: {},
        id: '1700057501196',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501196',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454209,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501189,
        format: {},
        id: '1700057501189',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501189',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454238,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501229,
        format: {
          contentType: 'link',
        },
        id: '1700057501229',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501229',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454330,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501228,
        format: {
          contentType: 'link',
        },
        id: '1700057501228',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501228',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454355,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815728,
        format: {
          contentType: 'link',
        },
        id: '1700057815728',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057815728',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454386,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501211,
        format: {},
        id: '1700057501211',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501211',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454411,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815729,
        format: {
          contentType: 'link',
        },
        id: '1700057815729',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057815729',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454440,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501203,
        format: {},
        id: '1700057501203',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501203',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454477,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501225,
        format: {},
        id: '1700057501225',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501225',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454508,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501205,
        format: {
          contentType: 'link',
        },
        id: '1700057501205',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501205',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454541,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501207,
        format: {},
        id: '1700057501207',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501207',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454570,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501208,
        format: {
          contentType: 'link',
        },
        id: '1700057501208',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501208',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454617,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501209,
        format: {
          contentType: 'link',
        },
        id: '1700057501209',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501209',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454630,
      },
    ],
    display: {
      conf: {
        alignDimension: 'left',
        alignMeasure: 'right',
        alternateRow: true,
        alternateRowColor: '^#FBFBFC',
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: '^#141414',
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: 'tight',
        columnWidth: [],
        compact: false,
        compactDirection: 'horizontal',
        customFields: {
          enable: true,
        },
        display: 'standard',
        fixedIndex: -1,
        gridLineColor: '^#E1E4E8',
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: 'solid',
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: 'solid',
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: 'solid',
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: '^#EEF1F5',
        headerFontBold: true,
        headerFontColor: '^#1B1F23',
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: 'row',
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 20,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: 'loose',
        sortable: false,
        specialValue: {
          dimensions: 'null',
          measures: 'bracketTxt',
        },
        tableStyle: 'standard',
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: 'table',
      type: 'table',
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501226,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501226',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501226',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454665,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501212,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501212',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501212',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454694,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501230,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501230',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501230',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454721,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501220,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501220',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501220',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454748,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501221,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501221',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501221',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454773,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501222,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501222',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501222',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454802,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501223,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501223',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501223',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454827,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501213,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501213',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501213',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454856,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501214,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501214',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501214',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454885,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501215,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501215',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501215',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454918,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501218,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501218',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501218',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454955,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501224,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501224',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501224',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454986,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501227,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501227',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501227',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455011,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501234,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501234',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501234',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455044,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501237,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501237',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501237',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455069,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501236,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501236',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501236',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455094,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501238,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501238',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501238',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455119,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815734,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: '1700057815734',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057815734',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455152,
      },
    ],
    pagination: {
      frontOnlyOffset: 20,
      queryKey: 'f2bb757d-4603-4eb6-9677-8573f9fa3e68',
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: 'Normal',
      structType: 'LeftRight',
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dataSetId: 3096130,
        dataTypeName: 'date',
        dimMetId: 1700057501188,
        filter: {
          op: 'lastSync',
          option: {
            dateMode: 'relative',
            isDefaultPartitionField: true,
            isReportFilter: false,
            isWhereInAggr: true,
          },
          val: [1],
          valOption: {
            anchorOffset: 0,
            datetimeUnit: 'day',
            hourSharp: true,
          },
        },
        format: {},
        highlight: false,
        id: '1700057501188',
        index: 0,
        location: 'whereList',
        name: 'p_date[app_store_enterprise_short_video_bi_core_df]',
        originId: '1700057501188',
        preRelation: 'and',
        roleType: 0,
        showEditComponent: false,
        undraggable: false,
        uniqueId: 250509180454011,
        unremovable: true,
      },
      {
        aggrConf: {},
        dimMetId: 250509180455293,
        filter: {
          children: [
            {
              aggrConf: {},
              dataSetId: 3096130,
              dimMetId: 1700057501191,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              filter: {
                op: 'last',
                option: {
                  dateMode: 'relative',
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: 'day',
                  hourSharp: true,
                },
              },
              format: {},
              id: '1700057501191',
              inCombinationPill: true,
              isMetric: false,
              location: 'whereList',
              name: '^短^视^频^发^布^日^期',
              originId: '1700057501191',
              preRelation: 'and',
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250509180455351,
              unremovable: true,
            },
            {
              aggrConf: {},
              dataSetId: 3096130,
              dimMetId: 1700057501192,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              filter: {
                op: 'last',
                option: {
                  dateMode: 'relative',
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: 'day',
                  hourSharp: true,
                },
              },
              format: {},
              id: '1700057501192',
              inCombinationPill: true,
              isMetric: false,
              location: 'whereList',
              name: '^数^据^表^现^日^期',
              originId: '1700057501192',
              preRelation: 'and',
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250509180455352,
              unremovable: true,
            },
          ],
          op: 'and',
        },
        format: {
          displayName: '^组^合^筛^选',
        },
        highlight: false,
        id: '250509180455293',
        index: 1,
        location: 'whereList',
        nameIndex: 1,
        notJoinQuery: false,
        originId: '250509180455293',
        pillType: 'combination_filter',
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250509180455293,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
  display: {
    conf: {
      alignDimension: 'left',
      alignMeasure: 'right',
      alternateRow: true,
      alternateRowColor: '^#FBFBFC',
      autoWrap: false,
      bodyFontBold: false,
      bodyFontColor: '^#141414',
      bodyFontItalic: false,
      bodyFontSize: 12,
      bodyFontUnderline: false,
      colPadding: null,
      colSpaceMode: 'tight',
      columnWidth: [],
      compact: false,
      compactDirection: 'horizontal',
      customFields: {
        enable: true,
      },
      display: 'standard',
      fixedIndex: -1,
      gridLineColor: '^#E1E4E8',
      gridLineFrame: true,
      gridLineFrameColor: null,
      gridLineFrameStyle: 'solid',
      gridLineFrameWidth: 1,
      gridLineHorizontal: true,
      gridLineHorizontalColor: null,
      gridLineHorizontalStyle: 'solid',
      gridLineHorizontalWidth: 1,
      gridLineVertical: true,
      gridLineVerticalColor: null,
      gridLineVerticalStyle: 'solid',
      gridLineVerticalWidth: 1,
      headerBackground: true,
      headerColor: '^#EEF1F5',
      headerFontBold: true,
      headerFontColor: '^#1B1F23',
      headerFontItalic: false,
      headerFontSize: 12,
      headerFontUnderline: false,
      headerMenu: true,
      headerSubTitleFontBold: false,
      headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
      headerSubTitleFontItalic: false,
      headerSubTitleFontSize: 12,
      headerSubTitleFontUnderline: false,
      hideHeader: false,
      hoverHighlight: 'row',
      lineNumber: false,
      loadPartialData: false,
      measureFirst: false,
      pageSize: 20,
      pagination: true,
      rowPadding: null,
      rowSpaceMode: 'loose',
      sortable: false,
      specialValue: {
        dimensions: 'null',
        measures: 'bracketTxt',
      },
      tableStyle: 'standard',
      transpose: false,
      version: 33,
    },
    enableAdvisor: true,
    queryType: 'table',
    type: 'table',
    fieldsFormat: {
      '1700057501191': {},
      '1700057501192': {},
      '1700057501193': {},
      '1700057501194': {
        contentType: 'link',
      },
      '1700057501195': {
        contentType: 'link',
      },
      '1700057501196': {},
      '1700057501189': {},
      '1700057501229': {
        contentType: 'link',
      },
      '1700057501228': {
        contentType: 'link',
      },
      '1700057815728': {
        contentType: 'link',
      },
      '1700057501211': {},
      '1700057815729': {
        contentType: 'link',
      },
      '1700057501203': {},
      '1700057501225': {},
      '1700057501205': {
        contentType: 'link',
      },
      '1700057501207': {},
      '1700057501208': {
        contentType: 'link',
      },
      '1700057501209': {
        contentType: 'link',
      },
      sum_1700057501226: {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501212: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501230: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501220: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501221: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501222: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501223: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501213: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501214: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501215: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501218: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501224: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501227: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501234: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501237: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501236: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      sum_1700057501238: {
        dataTypeName: 'int',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
      '1700057815734': {
        dataTypeName: 'float',
        numFormat: {
          auto: true,
          kSep: true,
          precision: 4,
          precisionType: 'significantDecimal',
          type: 'digit',
          unit: null,
        },
      },
    },
  },
  originalSchema: {
    annotation: {
      hash: 'd751713988987e9331980363e24189ce',
    },
    cache: {
      cacheVersion: 'V1',
      enable: true,
      expire: 300,
    },
    colors: [],
    columns: [],
    dimensions: [
      {
        aggrConf: {},
        dimMetId: 1700057501191,
        format: {},
        id: '1700057501191',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501191',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454042,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501192,
        format: {},
        id: '1700057501192',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501192',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454077,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501193,
        format: {},
        id: '1700057501193',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501193',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454283,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501194,
        format: {
          contentType: 'link',
        },
        id: '1700057501194',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501194',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454153,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501195,
        format: {
          contentType: 'link',
        },
        id: '1700057501195',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501195',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454178,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501196,
        format: {},
        id: '1700057501196',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501196',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454209,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501189,
        format: {},
        id: '1700057501189',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501189',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454238,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501229,
        format: {
          contentType: 'link',
        },
        id: '1700057501229',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501229',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454330,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501228,
        format: {
          contentType: 'link',
        },
        id: '1700057501228',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501228',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454355,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815728,
        format: {
          contentType: 'link',
        },
        id: '1700057815728',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057815728',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454386,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501211,
        format: {},
        id: '1700057501211',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501211',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454411,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815729,
        format: {
          contentType: 'link',
        },
        id: '1700057815729',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057815729',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454440,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501203,
        format: {},
        id: '1700057501203',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501203',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454477,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501225,
        format: {},
        id: '1700057501225',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501225',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454508,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501205,
        format: {
          contentType: 'link',
        },
        id: '1700057501205',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501205',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454541,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501207,
        format: {},
        id: '1700057501207',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501207',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454570,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501208,
        format: {
          contentType: 'link',
        },
        id: '1700057501208',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501208',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454617,
      },
      {
        aggrConf: {},
        dimMetId: 1700057501209,
        format: {
          contentType: 'link',
        },
        id: '1700057501209',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'dimensions',
        originId: '1700057501209',
        roleType: 0,
        type: 'string',
        uniqueId: 250509180454630,
      },
    ],
    display: {
      conf: {
        alignDimension: 'left',
        alignMeasure: 'right',
        alternateRow: true,
        alternateRowColor: '^#FBFBFC',
        autoWrap: false,
        bodyFontBold: false,
        bodyFontColor: '^#141414',
        bodyFontItalic: false,
        bodyFontSize: 12,
        bodyFontUnderline: false,
        colPadding: null,
        colSpaceMode: 'tight',
        columnWidth: [],
        compact: false,
        compactDirection: 'horizontal',
        customFields: {
          enable: true,
        },
        display: 'standard',
        fixedIndex: -1,
        gridLineColor: '^#E1E4E8',
        gridLineFrame: true,
        gridLineFrameColor: null,
        gridLineFrameStyle: 'solid',
        gridLineFrameWidth: 1,
        gridLineHorizontal: true,
        gridLineHorizontalColor: null,
        gridLineHorizontalStyle: 'solid',
        gridLineHorizontalWidth: 1,
        gridLineVertical: true,
        gridLineVerticalColor: null,
        gridLineVerticalStyle: 'solid',
        gridLineVerticalWidth: 1,
        headerBackground: true,
        headerColor: '^#EEF1F5',
        headerFontBold: true,
        headerFontColor: '^#1B1F23',
        headerFontItalic: false,
        headerFontSize: 12,
        headerFontUnderline: false,
        headerMenu: true,
        headerSubTitleFontBold: false,
        headerSubTitleFontColor: 'rgba(20, 20, 20, 0.45)',
        headerSubTitleFontItalic: false,
        headerSubTitleFontSize: 12,
        headerSubTitleFontUnderline: false,
        hideHeader: false,
        hoverHighlight: 'row',
        lineNumber: false,
        loadPartialData: false,
        measureFirst: false,
        pageSize: 20,
        pagination: true,
        rowPadding: null,
        rowSpaceMode: 'loose',
        sortable: false,
        specialValue: {
          dimensions: 'null',
          measures: 'bracketTxt',
        },
        tableStyle: 'standard',
        transpose: false,
        version: 33,
      },
      enableAdvisor: true,
      queryType: 'table',
      type: 'table',
    },
    drill: [],
    extensions: {
      data: {},
      list: [],
      protocolVersion: 1,
    },
    measures: [
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501226,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501226',
        index: 0,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501226',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454665,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501212,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501212',
        index: 1,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501212',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454694,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501230,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501230',
        index: 2,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501230',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454721,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501220,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501220',
        index: 3,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501220',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454748,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501221,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501221',
        index: 4,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501221',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454773,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501222,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501222',
        index: 5,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501222',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454802,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501223,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501223',
        index: 6,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501223',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454827,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501213,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501213',
        index: 7,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501213',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454856,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501214,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501214',
        index: 8,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501214',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454885,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501215,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501215',
        index: 9,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501215',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454918,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501218,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501218',
        index: 10,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501218',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454955,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501224,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501224',
        index: 11,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501224',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180454986,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501227,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501227',
        index: 12,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501227',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455011,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501234,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501234',
        index: 13,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501234',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455044,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501237,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501237',
        index: 14,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501237',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455069,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501236,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501236',
        index: 15,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501236',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455094,
      },
      {
        aggrConf: {
          exprAggr: 'sum(',
        },
        dimMetId: 1700057501238,
        format: {
          dataTypeName: 'int',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: 'sum_1700057501238',
        index: 16,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057501238',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455119,
      },
      {
        aggrConf: {},
        dimMetId: 1700057815734,
        format: {
          dataTypeName: 'float',
          numFormat: {
            auto: true,
            kSep: true,
            precision: 4,
            precisionType: 'significantDecimal',
            type: 'digit',
            unit: null,
          },
        },
        id: '1700057815734',
        index: 17,
        isGeoField: false,
        isMetric: false,
        location: 'measures',
        originId: '1700057815734',
        roleType: 1,
        type: 'string',
        uniqueId: 250509180455152,
      },
    ],
    pagination: {
      frontOnlyOffset: 20,
      queryKey: 'f2bb757d-4603-4eb6-9677-8573f9fa3e68',
      size: 50000,
    },
    parameters: [],
    periodCompare: [],
    realMetricTableRouteConfig: {
      isRealMetricQuery: false,
    },
    referenceLine: [],
    reportFilterConfig: {
      layoutSize: 'Normal',
      structType: 'LeftRight',
    },
    rows: [],
    sizes: [],
    subMeasures: [],
    tableCalculation: {
      rules: [],
    },
    whereList: [
      {
        aggrConf: {},
        dataSetId: 3096130,
        dataTypeName: 'date',
        dimMetId: 1700057501188,
        filter: {
          op: 'lastSync',
          option: {
            dateMode: 'relative',
            isDefaultPartitionField: true,
            isReportFilter: false,
            isWhereInAggr: true,
          },
          val: [1],
          valOption: {
            anchorOffset: 0,
            datetimeUnit: 'day',
            hourSharp: true,
          },
        },
        format: {},
        highlight: false,
        id: '1700057501188',
        index: 0,
        location: 'whereList',
        name: 'p_date[app_store_enterprise_short_video_bi_core_df]',
        originId: '1700057501188',
        preRelation: 'and',
        roleType: 0,
        showEditComponent: false,
        undraggable: false,
        uniqueId: 250509180454011,
        unremovable: true,
      },
      {
        aggrConf: {},
        dimMetId: 250509180455293,
        filter: {
          children: [
            {
              aggrConf: {},
              dataSetId: 3096130,
              dimMetId: 1700057501191,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              filter: {
                op: 'last',
                option: {
                  dateMode: 'relative',
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [30],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: 'day',
                  hourSharp: true,
                },
              },
              format: {},
              id: '1700057501191',
              inCombinationPill: true,
              isMetric: false,
              location: 'whereList',
              name: '^短^视^频^发^布^日^期',
              originId: '1700057501191',
              preRelation: 'and',
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250509180455351,
              unremovable: true,
            },
            {
              aggrConf: {},
              dataSetId: 3096130,
              dimMetId: 1700057501192,
              disableMenuKey: ['addOrFilter', 'subFilter'],
              filter: {
                op: 'last',
                option: {
                  dateMode: 'relative',
                  isReportFilter: false,
                  isWhereInAggr: true,
                },
                val: [1],
                valOption: {
                  anchorOffset: 1,
                  datetimeUnit: 'day',
                  hourSharp: true,
                },
              },
              format: {},
              id: '1700057501192',
              inCombinationPill: true,
              isMetric: false,
              location: 'whereList',
              name: '^数^据^表^现^日^期',
              originId: '1700057501192',
              preRelation: 'and',
              roleType: 0,
              showEditComponent: true,
              uniqueId: 250509180455352,
              unremovable: true,
            },
          ],
          op: 'and',
        },
        format: {
          displayName: '^组^合^筛^选',
        },
        highlight: false,
        id: '250509180455293',
        index: 1,
        location: 'whereList',
        nameIndex: 1,
        notJoinQuery: false,
        originId: '250509180455293',
        pillType: 'combination_filter',
        roleType: 0,
        showEditComponent: false,
        uniqueId: 250509180455293,
        unremovable: false,
      },
    ],
    whiteList: [],
  },
};
