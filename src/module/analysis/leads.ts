import { Page } from 'playwright';
import { Log } from 'crawlee';
import { formatLocalStringNumber } from '../../utils/data.js';

/**
 * 收集经营视图页面需要的相关数据
 */
export async function collectLeadsData(page: Page, log: Log) {
  const allLeadsData = page.locator('div[data-log-name="总线索量"]');

  let data: any = {};

  try {
    // 等待数据加载完成
    await page.waitForSelector('div[data-log-name="总线索量"]', {
      timeout: 10000,
    });

    const clueCountTotal = await allLeadsData
      .getByText('总线索量')
      .locator('..')
      .locator('p')
      .nth(1)
      .textContent();
    log.info('总线索量:', { clueCountTotal });

    const directLeadsText = await allLeadsData
      .getByText('直接线索量')
      .locator('..')
      .locator('..')
      .locator('p')
      .nth(1)
      .textContent();
    log.info('直接线索量', { directLeadsText });

    const indirectLeadsText = await allLeadsData
      .getByText('间接线索量')
      .locator('..')
      .locator('..')
      .locator('p')
      .nth(1)
      .textContent();
    log.info('间接线索量', { indirectLeadsText });

    const natureClueCount = await allLeadsData
      .getByText('自然免费线索量')
      .locator('..')
      .locator('..')
      .locator('p')
      .nth(1)
      .textContent();
    log.info('自然免费线索量', { natureClueCount });

    const totalAdCost = await allLeadsData
      .getByText('总广告消耗（元）')
      .locator('..')
      .locator('..')
      .locator('p')
      .nth(1)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('总广告消耗（元）', { totalAdCost });

    data = {
      clueCountTotal,
      natureClueCount,
      totalAdCost,
      adClueCount: Number(directLeadsText) + Number(indirectLeadsText),
    };

    return data;
  } catch (error) {
    log.error('收集线索数据时出错:', { error });
    return {};
  }
}
