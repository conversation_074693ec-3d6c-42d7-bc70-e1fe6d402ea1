import { Page } from 'playwright';
import { Log } from 'crawlee';
import {
  formatLocalStringNumber,
  parseTimeToSeconds,
} from '../../utils/data.js';

/**
 * 收集直播列表页面需要的相关数据
 */
export async function collectLiveData(page: Page, log: Log) {
  const liveData = page.locator('div[data-log-module="数据概览"]');

  let data: any = {};

  try {
    // 等待数据加载完成
    await page.waitForSelector('div[data-log-module="数据概览"]', {
      timeout: 10000,
    });

    const liveAdCost = await liveData
      .getByText('直播广告消耗')
      .locator('xpath=ancestor::section')
      .locator('text=元')
      .locator('..')
      .textContent()
      .then((content) => (content ? content.split(' ')[0].trim() : null))
      .then((content) => formatLocalStringNumber(content));
    log.info('直播广告消耗', { liveAdCost });

    const liveAvgWatchDuration = await liveData
      .getByText('直播人均观看时长')
      .locator('xpath=ancestor::section')
      .locator('text=秒')
      .locator('..')
      .textContent()
      .then((content) => (content ? parseTimeToSeconds(content) : null));
    log.info('直播人均观看时长（秒）', { liveAvgWatchDuration });

    const liveClueConvertRate = await liveData
      .getByText('直播线索转化率')
      .locator('xpath=ancestor::section')
      .locator('text=%')
      .locator('..')
      .textContent()
      .then((content) => (content ? content.split('%')[0].trim() : null));
    log.info('直播间线索转化率', { liveClueConvertRate });

    const liveCostPerClue = await liveData
      .getByText('直播线索成本')
      .locator('xpath=ancestor::section')
      .locator('text=元')
      .locator('..')
      .textContent()
      .then((content) => (content ? content.split(' ')[0].trim() : null))
      .then((content) => formatLocalStringNumber(content));
    log.info('直播线索成本', { liveCostPerClue });

    const liveFormClueCount = await liveData
      .getByText('直播间表单线索量')
      .locator('xpath=ancestor::section')
      .locator('div')
      .nth(1)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('直播间表单线索量', { liveFormClueCount });

    const liveInteractRate = await liveData
      .getByText('直播互动率')
      .locator('xpath=ancestor::section')
      .locator('text=%')
      .locator('..')
      .textContent()
      .then((content) => (content ? content.split('%')[0].trim() : null));
    log.info('直播互动率', { liveInteractRate });

    const livePrivateMsg24hClueCount = await liveData
      .getByText('直播引导私信24小时留资次数')
      .locator('xpath=ancestor::section')
      .locator('div')
      .nth(1)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('直播引导私信24小时留资次数', { livePrivateMsg24hClueCount });

    const liveSessionCount = await liveData
      .getByText('直播场次')
      .locator('xpath=ancestor::section')
      .locator('div')
      .nth(1)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('直播间场次', { liveSessionCount });

    data = {
      liveAdCost,
      liveAvgWatchDuration,
      liveClueConvertRate,
      liveCostPerClue,
      liveFormClueCount,
      liveInteractRate,
      livePrivateMsg24hClueCount,
      liveSessionCount,
    };

    return data;
  } catch (error) {
    log.error('收集直播列表数据页时出错:', { error });
    return {};
  }
}
