import { Page } from 'playwright';
import { Log } from 'crawlee';
import { formatLocalStringNumber } from '../../utils/data.js';

/**
 * 收集私信分析页面需要的相关数据
 */
export async function collectMessageData(page: Page, log: Log) {
  const overAllData = page.locator('div[data-log-module="整体数据"]');

  let data: any = {};

  try {
    await page.waitForSelector('div[data-log-module="整体数据"]', {
      timeout: 10000,
    });

    const privateMsgSessionUcount = await overAllData
      .getByText('进入私信会话人数')
      .locator('xpath=ancestor::div[3]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));

    log.info('进入私信会话人数', { privateMsgSessionUcount });

    const privateMsgOpenerUcount = await overAllData
      .getByText('私信会话开口人数')
      .locator('xpath=ancestor::div[3]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('私信会话开口人数', { privateMsgOpenerUcount });

    const privateMsgClueUcount = await overAllData
      .getByText('私信线索人数')
      .locator('xpath=ancestor::div[3]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('私信线索人数', { privateMsgClueUcount });

    const reply3minRate = await overAllData
      .getByText('3mins回复率')
      .locator('xpath=ancestor::div[3]')
      .locator('div')
      .nth(2)
      .textContent();
    log.info('3分钟内回复率', { reply3minRate });

    const privateMsgReceiveCount = await overAllData
      .getByText('接收消息数')
      .locator('xpath=ancestor::div[3]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));
    log.info('私信接收消息数', { privateMsgReceiveCount });

    data = {
      privateMsgSessionUcount,
      privateMsgOpenerUcount,
      privateMsgClueUcount,
      reply3minRate: reply3minRate?.trim().replace('%', ''),
    };

    return data;
  } catch (error) {
    log.error('收集私信数据时出错:', { error });
    return {};
  }
}
