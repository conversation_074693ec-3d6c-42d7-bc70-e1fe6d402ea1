import { Locator, Page } from 'playwright';
import { Log } from 'crawlee';
import { formatLocalStringNumber } from '../../utils/data.js';

/**
 * 收集短视频列表页面需要的相关数据
 */
export async function collectVideoData(page: Page, log: Log) {
  const videoData = page.locator('div[data-log-module="数据概览"]');

  const extractNumber = (locator: Locator, unit: string) => {
    return locator
      .locator(`text=${unit}`)
      .locator('..')
      .textContent()
      .then((content) => {
        return content ? content.split(unit)[0].trim() : null;
      });
  };

  let data: any = {};

  try {
    // 等待数据加载完成
    await page.waitForSelector('div[data-log-module="数据概览"]', {
      timeout: 10000,
    });

    const postAdCost = await extractNumber(
      videoData.locator('div[data-log-name="广告消耗"]'),
      '元'
    );
    const postCostPerClue = await extractNumber(
      videoData.locator('div[data-log-name="单条线索成本"]'),
      '元'
    );
    const postClueConvertRate = await videoData
      .locator('div[data-log-name="线索转化率"]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => {
        return content ? content.split('%')[0].trim() : null;
      });

    const postCount = await videoData
      .locator('div[data-log-name="新发布视频数"]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));

    const postClueCount = await videoData
      .locator('div[data-log-name="线索量"]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));

    const postPrivateMsgClueCount = await videoData
      .locator('div[data-log-name="引导私信线索量"]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));

    const postToLiveRedirectCount = await videoData
      .locator('div[data-log-name="引流直播间次数"]')
      .locator('div')
      .nth(2)
      .textContent()
      .then((content) => formatLocalStringNumber(content));

    log.info('广告消耗', { postAdCost });
    log.info('单条线索成本', { postCostPerClue });
    log.info('线索转化率', { postClueConvertRate });

    data = {
      postAdCost: formatLocalStringNumber(postAdCost),
      postCount,
      postClueCount,
      postCostPerClue: formatLocalStringNumber(postCostPerClue),
      postClueConvertRate,
      postPrivateMsgClueCount,
      postToLiveRedirectCount,
    };

    return data;
  } catch (error) {
    log.error('收集视频列表页数据时出错:', { error });
    return {};
  }
}
