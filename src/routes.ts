import { createPlaywrightRouter } from 'crawlee';
import { collectLeadsData } from './module/analysis/leads.js';
import { collectMessageData } from './module/analysis/message.js';
import { collectLiveData } from './module/analysis/live.js';
import { collectVideoData } from './module/analysis/video.js';
import { PageConfig, processSubAccounts } from './utils/dom.js';

export const router = createPlaywrightRouter();

router.addHandler('OVERVIEW', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="经营视图-全局筛选器"]',
    collectDataFn: collectLeadsData,
    pageName: '数据分析经营视图',
  };

  await processSubAccounts(page, config, log, '子账户');
});

router.addHandler('MESSAGE', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="私域-主页分析-全局筛选器"]',
    collectDataFn: collectMessageData,
    pageName: '数据分析私信分析',
  };

  await processSubAccounts(page, config, log, '子账户');
});

router.addHandler('LIVE-LIST', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="直播-直播列表-全局筛选器"]',
    collectDataFn: collectLiveData,
    pageName: '数据分析直播列表页',
  };

  await processSubAccounts(page, config, log, '子账户');
});

router.addHandler('VIDEO-LIST', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="短视频-视频列表-全局筛选器"]',
    collectDataFn: collectVideoData,
    pageName: '数据分析短视频列表页',
  };

  await processSubAccounts(page, config, log, '子账户');
});

router.addHandler('SPECIAL-OVERVIEW', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="经营视图-全局筛选器"]',
    collectDataFn: collectLeadsData,
    pageName: '数据分析经营视图',
  };

  await processSubAccounts(page, config, log, '员工号');
});

router.addHandler('SPECIAL-MESSAGE', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="私域-主页分析-全局筛选器"]',
    collectDataFn: collectMessageData,
    pageName: '数据分析私信分析',
  };

  await processSubAccounts(page, config, log, '员工号');
});

router.addHandler('SPECIAL-LIVE-LIST', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="直播-直播列表-全局筛选器"]',
    collectDataFn: collectLiveData,
    pageName: '数据分析直播列表页',
  };

  await processSubAccounts(page, config, log, '员工号');
});

router.addHandler('SPECIAL-VIDEO-LIST', async ({ page, log }) => {
  const config: PageConfig = {
    globalFilterSelector: 'div[data-log-module="短视频-视频列表-全局筛选器"]',
    collectDataFn: collectVideoData,
    pageName: '数据分析短视频列表页',
  };

  await processSubAccounts(page, config, log, '员工号');
});
