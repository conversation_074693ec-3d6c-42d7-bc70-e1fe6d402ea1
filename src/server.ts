// Fastify 服务器配置
import { log } from 'crawlee';
import Fastify from 'fastify';
import * as cron from 'node-cron';
import { crawlerManager, runCrawler } from './crawler.js';
import { registerApiRoutes } from './api.js';
import { dongcheyundianDataSync } from './fetch-schedule/dongcheyundian/index.js';

// 创建 Fastify 实例
const fastify = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
      },
    },
  },
});

// 注册所有 API 接口
registerApiRoutes(fastify);

/**
 * 设置定时任务
 */
function setupCronJob() {
  // 每日上午 10:00 (中国时区) 执行爬虫任务
  // 表示每天 10 11 12 点执行
  const cronExpression = '0 0 10,11,12 * * *';
  // 测试
  // const cronExpression = '*/15 * * * *';
  cron.schedule(
    cronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行爬虫任务');

        // 检查是否已有爬虫在运行
        if (crawlerManager.isRunning()) {
          log.error('定时任务跳过：爬虫正在运行中');
          return;
        }
        await runCrawler();
        log.info('定时任务完成：爬虫执行成功');
      } catch (error) {
        log.error('定时任务失败：爬虫执行出错', { error });
      }
    },
    {
      timezone: 'Asia/Shanghai', // 中国时区
    }
  );
}

/**
 * 设置懂车云店数据同步定时任务
 */
function setupDongcheyundianCronJob() {
  // 每日上午 10:00 和 12:00 (中国时区) 执行懂车云店数据同步任务
  const cronExpression = '0 0 10,12 * * *';
  cron.schedule(
    cronExpression,
    async () => {
      try {
        log.info('定时任务触发：开始执行懂车云店数据同步任务');
        await dongcheyundianDataSync();
        log.info('定时任务完成：懂车云店数据同步执行成功');
      } catch (error) {
        log.error('定时任务失败：懂车云店数据同步执行出错', { error });
      }
    },
    {
      timezone: 'Asia/Shanghai', // 中国时区
    }
  );
}

/**
 * 启动服务器
 */
export async function startServer(port: number = 3000) {
  try {
    log.info(`正在启动服务器，端口: ${port}`);

    // 设置定时任务
    setupCronJob();

    // 启动服务器
    await fastify.listen({ port, host: '0.0.0.0' });
    log.info(`服务器已启动，监听端口: ${port}`);
    log.info(`健康检查端点: http://0.0.0.0:${port}/health`);
    log.info(`爬虫状态端点: http://0.0.0.0:${port}/api/crawler/status`);

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      log.info(`收到 ${signal} 信号，开始优雅关闭...`);
      try {
        await fastify.close();
        log.info('服务器已优雅关闭');
        process.exit(0);
      } catch (error) {
        log.error('关闭服务器时出错:', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    log.error('服务器启动失败:', { error });
    process.exit(1);
  }
}
