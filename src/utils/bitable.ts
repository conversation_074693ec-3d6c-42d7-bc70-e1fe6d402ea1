import lark from '@larksuiteoapi/node-sdk';
import { sendMessage } from '../api/webhook.js';

const BITABLE_CONFIG = {
  client: {
    appId: 'cli_a2fc8a653678900e',
    appSecret: 'fsN6W60ygAWlrbnFKO96pgBk1Q1YO8pz',
    disableTokenCache: false,
  },
  table: {
    appToken: 'UJz5bIBtfaH6k0sPRVRcsUllnIe',
    tableId: 'tbl11OHcJNM5Ime2',
    viewId: 'vewvUyvmaY',
  },
  tenantToken: '******************************************',
};

type BitableResponse = {
  data: {
    items: Array<{
      fields: {
        Cookie: Array<{
          text: string;
        }>;
      };
    }>;
  };
};

/**
 * 从飞书多维表格获取Cookie
 * @param index 表格中的行索引
 * @returns Cookie字符串
 */
async function getCookieFromBitable(index: number): Promise<any> {
  const client = new lark.Client(BITABLE_CONFIG.client);

  try {
    const response = (await client.bitable.v1.appTableRecord.search(
      {
        path: {
          app_token: BITABLE_CONFIG.table.appToken,
          table_id: BITABLE_CONFIG.table.tableId,
        },
        params: {
          page_size: 10,
        },
        data: {
          view_id: BITABLE_CONFIG.table.viewId,
          field_names: ['Cookie'],
        },
      },
      lark.withTenantToken(BITABLE_CONFIG.tenantToken)
    )) as BitableResponse;

    if (
      !response.data.items[index] ||
      !response.data.items[index].fields.Cookie[0]?.text
    ) {
      throw new Error('获取多维表格 Cookie 数据失败!');
    }

    return response.data.items[index].fields.Cookie[0].text;
  } catch (error) {
    await sendMessage('获取多维表格 Cookie 失败', error as string, 'fail');
    throw error;
  }
}

export const getDongCheYunDianCookieByBitable = () => getCookieFromBitable(0);
export const getCheYunDianCookieByBitable = () => getCookieFromBitable(1);
