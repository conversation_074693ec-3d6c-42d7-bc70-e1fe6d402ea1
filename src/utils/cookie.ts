// 解析cookie字符串为Playwright格式的函数
export function parseCookieString(
  cookieStr: string,
  domain: string = '.cluerich.com'
) {
  return cookieStr.split(';').map((cookie) => {
    const [name, value] = cookie.trim().split('=');
    return {
      name: name.trim(),
      value: value ? value.trim() : '',
      domain: domain,
      path: '/',
      httpOnly: false,
      secure: true,
      sameSite: 'Lax' as const,
    };
  });
}
