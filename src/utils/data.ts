import type { Log } from 'crawlee';
import { KeyValueStore } from 'crawlee';
import { UserImportData } from '../api/data-report.js';

/**
 * 取括号内的外显 ID
 * bZ探索空间上海店(***********) -> ***********
 */
export function getTextInsideParentheses(str: string | null): string | null {
  if (!str) return null;
  const match = str.match(/\(([^)]+)\)/);
  return match ? match[1] : null;
}

/**
 * 保存账号数据并合并同一账号的不同页面数据
 */
export async function saveAccountDataWithMerge(
  accountId: string,
  accountName: string,
  statDate: string,
  pageName: string,
  accountData: any,
  log: Log
): Promise<void> {
  try {
    // 使用 KeyValueStore 临时存储合并数据
    const kvStore = await KeyValueStore.open('account-merge-temp');

    // 获取已存在的账号数据
    const existingData: any = (await kvStore.getValue(accountId)) || {
      showAccountId: accountId,
      statDate,
    };

    // 合并不同页面的数据到同一个对象
    Object.assign(existingData, accountData);

    // 更新 KeyValueStore 中的数据
    await kvStore.setValue(accountId, existingData);

    log.info(`账号 ${accountName} 的 ${pageName} 数据已合并保存`);
  } catch (error) {
    log.error(`保存账号数据失败:`, { error });
    throw error;
  }
}

/**
 * 获取所有合并后的账号数据，返回 UserImportData[]
 */
export async function getAllMergedAccountData(
  log: Log
): Promise<UserImportData[]> {
  try {
    const kvStore = await KeyValueStore.open('account-merge-temp');
    const result: UserImportData[] = [];

    // 获取所有合并后的账号数据
    await kvStore.forEachKey(async (key) => {
      const accountData = await kvStore.getValue(key);
      if (accountData) {
        result.push(accountData as UserImportData);
      }
    });

    log.info(`获取到 ${result.length} 个账号的合并数据`);
    return result;
  } catch (error) {
    log.error('获取合并数据失败:', { error });
    throw error;
  }
}

export function parseTimeToSeconds(timeString: string) {
  const regex = /(?:(\d+)\s*小时\s*)?(?:(\d+)\s*分\s*)?(\d+)\s*秒/;
  const match = timeString.match(regex);

  if (match) {
    const hours = match[1] ? parseInt(match[1]) : 0; // 如果有小时则使用，没用则设为0
    const minutes = match[2] ? parseInt(match[2]) : 0; // 如果有分钟则使用，没用则设为0
    const seconds = parseInt(match[3]); // 秒数必须存在

    return hours * 3600 + minutes * 60 + seconds;
  } else {
    throw new Error('Invalid time format');
  }
}

// 去除科学计数法的逗号
export function formatLocalStringNumber(num: string | null) {
  if (!num) return null;
  return num.replace(/,/g, '');
}
