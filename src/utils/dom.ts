import type { Locator, Page } from 'playwright';
import type { Log } from 'crawlee';
import { getTextInsideParentheses, saveAccountDataWithMerge } from './data.js';

type SubAccount = {
  name: string;
  index: number;
};

/**
 * 等待元素出现并可见
 */
export async function waitForElement(
  page: Page,
  selector: string,
  log: Log,
  timeout: number = 10000
): Promise<boolean> {
  try {
    await page.locator(selector).first().waitFor({
      state: 'visible',
      timeout,
    });
    log.info(`元素已出现: ${selector}`);
    return true;
  } catch (error) {
    log.info(`等待元素超时: ${selector}`, { error });
    return false;
  }
}

/**
 * 等待数据加载完成
 */
export async function waitForDataLoad(
  page: Page,
  log: Log,
  timeout: number = 3000
): Promise<void> {
  log.info('等待数据加载...');

  try {
    // 等待网络空闲
    await page.waitForLoadState('networkidle', { timeout });
    log.info('数据加载完成');
  } catch (error) {
    log.info('等待数据加载超时，继续执行');
  }
}

/**
 * 获取所有子账号选项
 */
export async function getAllSubAccounts(
  page: Page,
  log: Log
): Promise<SubAccount[]> {
  try {
    const subAccountElements = await page
      .locator('.leads-cascader-item-label')
      .all();
    const subAccounts: SubAccount[] = [];

    for (let i = 0; i < subAccountElements.length; i++) {
      const element = subAccountElements[i];
      const name = await element.textContent();
      if (name && name.trim() !== '') {
        subAccounts.push({
          name: name.trim(),
          index: i,
        });
      }
    }

    log.info(`发现 ${subAccounts.length} 个子账号`);
    return subAccounts;
  } catch (error) {
    log.error('获取子账号列表失败:', { error });
    return [];
  }
}

/**
 * 打开子账号下拉框
 * 如果下拉面板未打开，会重试最多3次
 */
export async function openSubAccountDropdown(
  page: Page,
  globalFilterDom: Locator,
  log: Log
): Promise<boolean> {
  const maxRetries = 3;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      log.info(`打开子账号选择器 (尝试 ${attempt}/${maxRetries})`);

      // 点击下拉选择器
      await globalFilterDom
        .locator('.leads-cascader.leads-cascader-select')
        .click();
      await page.waitForTimeout(1000); // 等待下拉框展开

      // 等待下拉框面板出现
      const panelVisible = await waitForElement(
        page,
        '.leads-cascader-popover-panel',
        log
      );

      if (panelVisible) {
        log.info(`子账号下拉框成功打开 (尝试 ${attempt}/${maxRetries})`);
        return true;
      } else {
        log.info(`子账号下拉框面板未出现 (尝试 ${attempt}/${maxRetries})`);
        if (attempt < maxRetries) {
          log.info(`等待1秒后重试...`);
          await page.waitForTimeout(1000);
        }
      }
    } catch (error) {
      log.error(`打开子账号下拉框失败 (尝试 ${attempt}/${maxRetries}):`, {
        error,
      });
      if (attempt < maxRetries) {
        log.info(`等待1秒后重试...`);
        await page.waitForTimeout(1000);
      }
    }
  }

  log.error(`经过 ${maxRetries} 次尝试后，仍无法打开子账号下拉框`);
  return false;
}

/**
 * 清除已选中的账号选项
 * 当账号选择器中已有选中的账号时，hover到账号标签会显示清除按钮（X图标）
 * 点击清除按钮可以清除对应的选项
 */
export async function clearSelectedAccounts(page: any, log: any) {
  try {
    // 查找所有已选中的账号标签
    const selectedTags = await page.locator('.leads-tag').all();

    if (selectedTags.length === 0) {
      log.info('没有找到已选中的账号，无需清除');
      return;
    }

    log.info(`发现 ${selectedTags.length} 个已选中的账号，开始清除`);

    // 遍历所有已选中的标签，清除它们
    for (let i = selectedTags.length - 1; i >= 0; i--) {
      const tag = selectedTags[i];

      try {
        // 先hover到标签上，让清除按钮显示出来
        await tag.hover();
        await page.waitForTimeout(200); // 等待hover效果生效

        // 查找该标签内的清除按钮
        const closeButton = tag.locator('.leads-tag-close');

        // 检查清除按钮是否存在且可见
        const isVisible = await closeButton.isVisible();
        if (isVisible) {
          await closeButton.click();
          await page.waitForTimeout(300); // 等待清除操作完成
          log.info(`成功清除第 ${i + 1} 个已选中的账号`);
        } else {
          log.warn(`第 ${i + 1} 个账号的清除按钮不可见，尝试直接点击标签`);
          // 如果清除按钮不可见，尝试点击标签本身
          await tag.click();
          await page.waitForTimeout(300);
        }
      } catch (error) {
        log.error(`清除第 ${i + 1} 个账号时出错:`, error);
        // 继续处理下一个标签
        continue;
      }
    }

    // 等待所有清除操作完成
    await page.waitForTimeout(500);

    // 验证是否所有标签都已被清除
    const remainingTags = await page.locator('.leads-tag').count();
    if (remainingTags === 0) {
      log.info('所有已选中的账号已成功清除');
    } else {
      log.warn(`仍有 ${remainingTags} 个账号未被清除`);
    }
  } catch (error) {
    log.error('清除已选中账号时出错:', error);
    // 不抛出错误，继续执行后续逻辑
  }
}

/**
 * 页面配置接口 - 定义不同页面的特定配置
 */
export type PageConfig = {
  /** 全局筛选器的选择器 */
  globalFilterSelector: string;
  /** 数据收集函数 */
  collectDataFn: (pageOrDom: any, log: Log) => Promise<any>;
  /** 页面名称（用于日志） */
  pageName: string;
};

/**
 * 重新打开下拉框（用于选择下一个账号）
 */
export async function reopenDropdownForNext(
  page: Page,
  log: Log
): Promise<void> {
  try {
    await page.locator('.leads-cascader').click();
    await page.waitForTimeout(1000);
  } catch (error) {
    log.error('重新打开下拉框失败:', { error });
  }
}

/**
 * 通用子账号处理函数 - 处理子账号的遍历和数据收集
 */
export async function processSubAccounts(
  page: Page,
  config: PageConfig,
  log: Log,
  accountType: '子账户' | '员工号'
): Promise<void> {
  log.info(`开始爬取${config.pageName}`);

  try {
    await waitForDataLoad(page, log);

    const globalFilterDom = page.locator(config.globalFilterSelector);

    // 获取统计日期
    const value = await globalFilterDom
      .locator('input[placeholder="开始时间 ~ 结束时间"]')
      .inputValue();
    const statDate = value.split('~')[0].trim();

    // 点击设置子账户
    log.info('点击设置子账户');
    await globalFilterDom
      .locator('span.leads-radio-label', { hasText: accountType })
      .click();
    await waitForDataLoad(page, log);

    // 打开子账号下拉框
    const dropdownOpened = await openSubAccountDropdown(
      page,
      globalFilterDom,
      log
    );
    if (!dropdownOpened) {
      throw new Error('无法打开子账号下拉框');
    }

    // 获取所有子账号选项
    const subAccounts = await getAllSubAccounts(page, log);
    if (subAccounts.length === 0) {
      log.info('未找到任何子账号');
      return;
    }

    // 遍历每个子账号收集数据
    for (let i = 0; i < subAccounts.length; i++) {
      const subAccount = subAccounts[i];

      try {
        log.info(`开始收集子账号数据: ${subAccount.name}`);

        // 选择当前子账号
        const selected = await selectSubAccount(
          page,
          globalFilterDom,
          subAccount,
          log
        );
        if (!selected) {
          log.error(`选择子账号失败，跳过: ${subAccount.name}`);
          continue;
        }

        await page.waitForTimeout(2000);

        // 收集当前子账号的数据
        const accountData = await config.collectDataFn(page, log);

        // 保存数据 - 使用合并策略
        const accountId =
          getTextInsideParentheses(subAccount.name) || subAccount.name;

        await saveAccountDataWithMerge(
          accountId,
          subAccount.name,
          statDate,
          config.pageName,
          accountData,
          log
        );

        log.info(`子账号 ${subAccount.name} 数据收集完成:`, accountData);

        // 重新打开下拉框准备选择下一个账号（除了最后一个）
        if (i < subAccounts.length - 1) {
          await reopenDropdownForNext(page, log);
        }
      } catch (error) {
        log.error(`收集子账号 ${subAccount.name} 数据时出错:`, { error });
        // 继续处理下一个账号
        continue;
      }
    }
    log.info('所有子账号数据收集完成');
  } catch (error) {
    log.error('爬取过程中出现错误:', { error });
    throw error;
  }
}

/**
 * 选择指定的子账号
 */
export async function selectSubAccount(
  page: Page,
  globalFilterDom: any,
  subAccount: SubAccount,
  log: Log
): Promise<boolean> {
  try {
    // 检查下拉框是否可见，如果不可见则重新打开
    const cascaderVisible = await page
      .locator('.leads-cascader-popover-panel')
      .isVisible();

    if (!cascaderVisible) {
      const reopened = await openSubAccountDropdown(page, globalFilterDom, log);
      if (!reopened) {
        log.error(`无法重新打开子账号选择器，跳过账号: ${subAccount.name}`);
        return false;
      }
    }

    // 清除已选中的账号
    await clearSelectedAccounts(page, log);

    // 选择当前子账号
    const targetElement = page
      .locator('.leads-cascader-item-label')
      .nth(subAccount.index);
    await targetElement.click();

    await page.waitForTimeout(2000);

    // 失焦关闭下拉框才会获取数据
    await globalFilterDom.getByText('账号类型').click();
    await globalFilterDom.getByText('账号类型').click();
    await page.waitForTimeout(2000);
    await page.waitForLoadState('load', { timeout: 1000 });

    return true;
  } catch (error) {
    log.error(`选择子账号 ${subAccount.name} 失败:`, { error });
    return false;
  }
}
