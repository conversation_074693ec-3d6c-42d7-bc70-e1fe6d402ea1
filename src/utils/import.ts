import { sendMessage } from '../api/webhook.js';
import { defaultHeaders } from './config.js';

export async function importTableData(
  rowsData: any,
  url: string,
  tableName: string,
  title: string
) {
  const batchSize = 500;
  const rowLength = rowsData.length;
  let successCount = 0;
  let failCount = 0;
  let failMsgs: string[] = [];
  let lastTime = '';
  for (let i = 0; i < rowLength; i += batchSize) {
    const batch = rowsData.slice(i, i + batchSize);
    const response = await fetch(url, {
      headers: defaultHeaders,
      method: 'POST',
      body: JSON.stringify(batch),
    });
    console.log('importTableData===response', response);
    let data = await response.json();
    lastTime = data.time || lastTime;
    if (data.code === 0) {
      successCount += Array.isArray(batch) ? batch.length : 0;
    } else {
      failCount += Array.isArray(batch) ? batch.length : 0;
      failMsgs.push(`第${i / batchSize + 1}批失败：${data.msg || '未知错误'}`);
    }
  }
  const isDev = url.includes('dev');
  // 统一发送消息
  if (failCount === 0) {
    const content = `**同步成功${
      isDev ? '（测试环境）' : ' (正式环境)'
    }**\n\n- 同步表: ${tableName}\n- 同步条数：${successCount}\n- 同步时间：${lastTime}`;
    await sendMessage(title, content, 'success', isDev);
  } else {
    const content = `**同步部分失败${
      isDev ? '（测试环境）' : ' (正式环境)'
    }** <at id=all></at>\n\n- 同步表: ${tableName}\n- 成功条数：${successCount}\n- 失败条数：${failCount}\n- 失败详情：\n${failMsgs.join(
      '\n'
    )}\n- 同步时间：${lastTime}`;
    await sendMessage(title, content, 'fail', isDev);
  }
}
