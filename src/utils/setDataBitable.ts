import lark from '@larksuiteoapi/node-sdk';
import { UserImportData } from '../api/data-report.js';

/**
 * 将UserImportData字段映射为飞书表格字段
 */
function mapUserDataToBitableFields(
  userData: UserImportData
): Record<string, any> {
  return {
    外显ID: userData.showAccountId || '',
    统计日期: userData.statDate || '',
    自然线索数量: userData.natureClueCount || '0',
    广告线索数量: userData.adClueCount || 0,
    新媒体线索数量: userData.clueCountTotal || '0',
    私信开口人数: userData.privateMsgOpenerUcount || '0',
    私信线索人数: userData.privateMsgClueUcount || '0',
    进入私信会话人数: userData.privateMsgSessionUcount || '0',
    '3分钟回复率': userData.reply3minRate || '0',
    新发布视频数: userData.postCount || '0',
    广告消耗: userData.totalAdCost || '0',
    直播投流消耗: userData.liveAdCost || '0',
    作品投流消耗: userData.postAdCost || '0',
    短视频线索数: userData.postClueCount || '0',
    短视频单条线索成本: userData.postCostPerClue || '0',
    短视频线索转化率: userData.postClueConvertRate || '0',
    短视频引导私信线索量: userData.postPrivateMsgClueCount || '0',
    短视频引流直播间次数: userData.postToLiveRedirectCount || '0',
    直播间场次: userData.liveSessionCount || '0',
    直播间表单线索量: userData.liveFormClueCount || '0',
    直播间线索成本: userData.liveCostPerClue || '0',
    直播间互动率: userData.liveInteractRate || '0',
    直播间线索转化率: userData.liveClueConvertRate || '0',
    直播间引导私信24小时留资次数: userData.livePrivateMsg24hClueCount || '0',
    '直播人均观看时长(秒)': userData.liveAvgWatchDuration || 0,
    私信接收消息数: userData.privateMsgReceiveCount || '0',
  };
}

export async function insertCollectedDataToBitable(data: UserImportData[]) {
  const client = new lark.Client({
    appId: 'cli_a2fc8a653678900e',
    appSecret: 'fsN6W60ygAWlrbnFKO96pgBk1Q1YO8pz',
    disableTokenCache: false,
  });

  const result = await client.wiki.v2.space.getNode({
    params: {
      token: 'L8YTwdvaHiBozKka57ocRmsgnvH',
      obj_type: 'wiki',
    },
  });

  const bitableAppToken = result.data?.node?.obj_token;
  console.log('hjy ~ getBitable ~ bitableAppToken:', bitableAppToken);

  const newData = data.map((item) => {
    return {
      // @ts-ignore
      fields: mapUserDataToBitableFields(item),
    };
  });
  console.log('hjy ~ getBitable ~ newData:', newData);

  if (bitableAppToken) {
    client.bitable.v1.appTableRecord
      .batchCreate({
        path: {
          app_token: bitableAppToken,
          table_id: 'tblqrPpXW93jS15f',
        },
        data: {
          records: newData,
        },
      })
      .then((res) => {
        console.log(res);
      })
      .catch((e) => {
        console.error(JSON.stringify(e.response.data, null, 4));
      });
  }
}
