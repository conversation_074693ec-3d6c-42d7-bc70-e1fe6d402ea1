import { sendMessage } from '../api/webhook.js';
import { defaultHeaders } from './config.js';

export async function getTokenByCheyundian(cookie: string) {
  const url = 'https://leads.cluerich.com/bff/statistic/bi/user-token';
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        ...defaultHeaders,
        Cookie: cookie,
      },
    });

    const data = await response.json();
    return data.data.token;
  } catch (error) {
    const content = `**同步失败** <at id=all></at>\n\n- 失败原因：车云店 Cookie 过期，请访问飞书多维表格替换 Cookie \n- https://hj81r4bz4v.feishu.cn/base/UJz5bIBtfaH6k0sPRVRcsUllnIe?table=tbl11OHcJNM5Ime2&view=vewvUyvmaY \n- 打开已经登录巨懂车的浏览器，控制台打开网络，输入过滤出/token接口，获取请求头中的 Cookie 粘贴到表格中，操作步骤在多维表格评论处可参考`;
    await sendMessage('车云店数据同步失败', content, 'fail');
  }
}
